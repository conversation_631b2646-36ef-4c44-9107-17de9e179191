<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seat Selection</title>
    <!-- Bootstrap CSS for classic styling -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  

    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;

        }
        
     
        /* Custom Styles */
        .seat {
            width: 100px;
            height: 50px;
            margin: 5px;
            background-color: #ddd;
            border-radius: 5px;
            display: inline-block;
            text-align: center;
            line-height: 50px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .seat.selected {
            background-color: #28a745;
            color: white;
        }
        .seat.unavailable {
            background-color: #ff6666;
            cursor: not-allowed;
        }
        .seat:hover:not(.selected):not(.unavailable) {
            background-color: #007bff;
            color: white;
        }
        .seat-map .row.seatSel {
            display: inline;
        }
        .row {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .front {
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }
        


        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        /* .form-control {
            border: none;
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        } */

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }

       

        .card {
            border: none;
            border-radius: 1rem;
            background-color: #fff;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            margin-bottom: 20px; /* Added margin-bottom to avoid overlap with the footer */
        }

        .card-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            color: #f8f9fa;
            background: rgb(0,185,215);
            background: linear-gradient(283deg, rgba(0,185,215,0.9416141456582633) 0%, rgba(0,49,153,0.8883928571428571) 100%);
            text-align: center;
            padding: 20px;
        }

        .card-header h2 {
            font-size: 1.5rem;
        }

        .form-group label {
            font-weight: bold;
        }

        .form-group input[readonly] {
            background-color: #e9ecef;
            opacity: 1;
        }

        .btn-dark {
            background-color: #343a40;
            border-color: #343a40;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .btn-dark:hover {
            background-color: #23272b;
            border-color: #1d2124;
        }

        .form-row {
            margin-bottom: 15px;
        }

        /* Footer styling */
        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /* 
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        .seatSel{
            display: flex;
            justify-content: center;
            flex-direction: row;
            flex-wrap: wrap;
            margin-bottom: 10px;
            border-radius: 1rem;
        }
        .eachSeat{
            padding: 10px;
            border: 2px solid #28a746;
            color: #28a746;
            border-radius: 5px;
            margin: 10px;
            font-weight: 600;
        }
        #seatForm{
            width: fit-content;
            margin: 0 auto;
            /* margin-top: 10px; */
        }
        .shift-btn-cont{
            display: flex;
            justify-content: center;
            /* gap: 5px; */
        }
        .shift-btn{
            padding: 10px;
            border: 2px solid black;
            background-color: white;
            color: black;
            border-radius: 5px;
            margin: 10px;
            font-weight: 600;
        }

        .container {
            padding: 1rem;
        }

        .seat_heading {
            padding: 0;
            margin: 0;
            font-size: 1rem;
            line-height: 1.5rem;
            border-radius: 1rem;
        }

        @media only screen and (max-width: 767px) {
          .container {
            display: flex;
            justify-content: center;
            margin: 1rem;
            width: 90% !important;
          }
            .shift {
               text-align: center;
            }
        }

    </style>
</head>
<body>

 
    {% include "sidebar.html" %}

<div class="mt-5">


    <div class="container card">
        {% if messages %}
            {% for message in messages %}
            <div class="alert alert-primary alert-dismissible fade show" role="alert">
                {{ message }}<br>
            </div>
            {% endfor %}
        {% endif %}
        <div class="card-header" style="border-radius: 1rem;">
            <h2 class="seat_heading">Seat Selection for {{ student.name }}</h2>
        </div>
        <div class="card-body">
                <!-- Displaying student details as read-only text -->
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="student_name">Student Name:</label>
                        <input type="text" class="form-control" id="student_name" name="student_name"
                            value="{{ student.name }}" readonly>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="contact">Contact:</label>
                        <input type="text" class="form-control" id="contact" name="contact"
                            value="{{ student.mobile }}" readonly>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label for="email">Email:</label>
                        <input type="text" class="form-control" id="email" name="email" value="{{ student.email }}"
                            readonly>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="gender">Gender:</label>
                        <input type="text" class="form-control" id="gender" name="gender"
                            value="{{ student.gender }}" readonly>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="invoice_amount">Invoice Amount:</label>
                        <input type="invoice_amount" class="form-control" id="invoice_amount" name="invoice_amount" value="{{ invoice.total_amount }}"
                            readonly>
                    </div>
                </div>
            </form>
        </div>
    </div>


    <br>

    <!-- Seat Selection Section -->
    <div class="card m-4 p-4">
        <h5 class="font-weight-bold">Seats Selected: </h5>
        <div class="seatSel"></div>
        <h5 class="font-weight-bold">Select Your Seats</h5>
        <div class="shift-btn-cont">
            <button class="shift-btn px-4" onclick="filterSeats('all')">All</button>
            <!-- Render each shift as a button to filter seats -->
            {% for i in shifts %}
                <button class="shift-btn" onclick="filterSeats('{{ i }}')">{{ i }}</button>
            {% endfor %}
        </div>
        
        <div class="seat-map">
            <div class="row seatSel">
                <!-- Display all seats initially, but include shift data -->
                {% for seat in seats %}
                    <div 
                        class="seat {% if not seat.is_available %}unavailable{% endif %}" 
                        data-seat-id="{{ seat.id }}" 
                        data-seat-number="{{ seat.seat_number }}"
                        data-shift="{{ seat.shift }}"
                        onclick="selectSeat(this)"
                        style="display: none;"
                    >
                        {{ seat.seat_number }}
                    </div>
                {% endfor %}
            </div>
        </div>
        <!-- Submit Button -->
        <form action="" method="POST" id="seatForm">
            {% csrf_token %}
            <input type="hidden" name="selectedSeats" id="selectedSeats">
            <button type="submit" class="btn btn-primary btn-block mt-4">Confirm Seats</button>
        </form>
    </div>

</div>

<!-- Footer -->
<div class="footer">
    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
    <!-- <p>Developed with passion by Librainian</p> -->
</div>

<!-- filter seats by shifts -->
<script>
    function filterSeats(selectedShift) {
        // If 'all' is selected, display all seats
        if (selectedShift === 'all') {
            document.querySelectorAll('.seat').forEach(seat => {
                seat.style.display = 'inline-block';
            });
        } else {
            // Hide all seats first
            document.querySelectorAll('.seat').forEach(seat => {
                seat.style.display = 'none';
            });
            // Show seats that match the selected shift
            document.querySelectorAll(`.seat[data-shift="${selectedShift}"]`).forEach(seat => {
                seat.style.display = 'inline-block';
            });
        }
    }

    // Display all seats initially by default
    document.addEventListener("DOMContentLoaded", function() {
        filterSeats('all');
    });
</script>

<!-- JavaScript for seat selection -->
<script>
    function selectSeat(element) {
        // Check if seat is unavailable, ignore if so
        if (element.classList.contains('unavailable')) return;

        // Toggle selected class
        element.classList.toggle('selected');

        // Update selected seats in the hidden input
        const selectedSeats = [];
        // document.querySelectorAll('.seat.selected').forEach(seat => {
        //     selectedSeats.push(seat.getAttribute('data-seat-id'));
        // });
        // document.getElementById('selectedSeats').value = selectedSeats.join(',');
        const seatSelDiv = document.querySelector('.seatSel'); // Select the seatSel div
        seatSelDiv.innerHTML = ''; // Clear previous selections

        document.querySelectorAll('.seat.selected').forEach(seat => {
            const seatId = seat.getAttribute('data-seat-id');
            const seatNumber = seat.getAttribute('data-seat-number');
            const shift = seat.getAttribute('data-shift');
            selectedSeats.push(seatId);

            // Update the seatSel div with selected seats info
            seatSelDiv.innerHTML += `<div class="eachSeat">${seatNumber} - ${shift}</div>`;
        });

        document.getElementById('selectedSeats').value = selectedSeats.join(',');
    }
</script>

<script>
    const seatElements = document.querySelectorAll('.row .seat');

// Object to store seats grouped by shift
const seatsByShift = {};

seatElements.forEach(seatElement => {
  // Extract seat number and shift from data attributes
  const seatId = seatElement.dataset.seatId;
  const shift = seatElement.textContent.split(' ')[1]; // split on space

  // Create an array for the shift if it doesn't exist
  if (!seatsByShift[shift]) {
    seatsByShift[shift] = [];
  }

  // Add the seat element to the corresponding shift array
  seatsByShift[shift].push(seatElement);
});

// Clear the existing seat elements (optional)
const rowElement = document.querySelector('.row');
rowElement.innerHTML = '';

// Loop through each shift and append its seats to the row
Object.entries(seatsByShift).forEach(([shift, seats]) => {
  const shiftContainer = document.createElement('div');
  shiftContainer.classList.add('shift');
  shiftContainer.textContent = shift;

  seats.forEach(seatElement => {
    shiftContainer.appendChild(seatElement);
  });

  rowElement.appendChild(shiftContainer);
});
</script>

<!-- Bootstrap JS for additional components -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- JavaScript dependencies -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

</body>
</html>
