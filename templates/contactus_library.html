<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

    <!-- Primary Meta Tags -->
    <title>New Contact Message | {{ library_name }} | Librainian</title>
    <meta name="description" content="Contact message notification for {{ library_name }}. View details of the inquiry from {{ name }} regarding library services, hours, or facilities.">
    <meta name="keywords" content="library contact, {{ library_name }}, contact message, library inquiry, library communication, library management system, Librainian contact, library customer service, library support, library feedback, {{ library_name }} contact, library email notification">
    <meta name="author" content="Librainian">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="content-language" content="en">
    <link rel="canonical" href="https://www.librainian.com/contact-confirmation/">
    <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:url" content="https://www.librainian.com/contact-confirmation/">
    <meta property="og:title" content="Contact Message Confirmation | {{ library_name }}">
    <meta property="og:description" content="Your message to {{ library_name }} has been received. We'll respond to your inquiry shortly.">
    <meta property="og:image" content="https://www.librainian.com/static/img/contact-confirmation.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:title" content="Contact Message Confirmation | {{ library_name }}">
    <meta name="twitter:description" content="Your message to {{ library_name }} has been received. We'll respond to your inquiry shortly.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/contact-confirmation.jpg">

    <!-- Structured Data - Contact Page -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "name": "{{ library_name }} Contact Confirmation",
      "description": "Confirmation page for contact messages sent to {{ library_name }}",
      "mainEntity": {
        "@type": "Organization",
        "name": "{{ library_name }}",
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "email": "{{ email }}",
          "availableLanguage": ["English", "Hindi"]
        }
      }
    }
    </script>

    <!-- Structured Data - BreadcrumbList -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.librainian.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Contact",
          "item": "https://www.librainian.com/contact/"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Contact Confirmation",
          "item": "https://www.librainian.com/contact-confirmation/"
        }
      ]
    }
    </script>



    <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      

    <!-- Analytics Tracking -->



    <style>
        /* CSS Variables for consistent styling */
        :root {
            --primary-color: #0056b3;
            --primary-dark: #004494;
            --secondary-color: #f9f9f9;
            --text-color: #333333;
            --text-light: #666666;
            --text-white: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 0 15px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        /* Base Styles */
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            padding: 0;
            font-family: 'Roboto', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f4f4f4;
            font-size: 16px;
        }

        /* Import Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        /* Container Styles */
        .container {
            max-width: 650px;
            margin: 30px auto;
            background-color: #ffffff;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        /* Header Styles */
        .header {
            background-color: var(--primary-color);
            color: var(--text-white);
            padding: 25px 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 26px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #004494, #0077cc);
        }

        /* Logo */
        .logo {
            display: block;
            margin: 0 auto 15px;
            width: 80px;
            height: auto;
        }

        /* Content Styles */
        .content {
            padding: 30px;
        }

        .content p {
            margin-bottom: 15px;
            line-height: 1.7;
        }

        /* Message Details Styles */
        .message-details {
            background-color: var(--secondary-color);
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }

        .message-details p {
            margin: 10px 0;
        }

        .message-details strong {
            color: var(--primary-dark);
            font-weight: 500;
        }

        /* Footer Styles */
        .footer {
            background-color: #eeeeee;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: var(--text-light);
            border-top: 1px solid #e0e0e0;
        }

        .footer p {
            margin: 0;
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* Social Links */
        .social-links {
            margin-top: 15px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: var(--text-light);
            transition: var(--transition);
        }

        .social-links a:hover {
            color: var(--primary-color);
        }

        /* Responsive Styles */
        @media only screen and (max-width: 700px) {
            .container {
                width: 95%;
                margin: 15px auto;
            }

            .content {
                padding: 20px;
            }
        }

        @media only screen and (max-width: 480px) {
            .container {
                width: 100%;
                margin: 0;
                border-radius: 0;
            }

            .header h1 {
                font-size: 22px;
            }

            .content {
                padding: 15px;
            }

            .message-details {
                padding: 15px;
            }
        }
    </style>
</head>
<body>


    <div class="container" role="main">
        <div class="header">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" class="logo">
            <h1>New Contact Message</h1>
        </div>

        <div class="content">
            <p>Dear <strong>{{ library_name }}</strong> Administrator,</p>

            <p>You have received a new contact message from your library website. Details of the message are as follows:</p>

            <div class="message-details">
                <p><strong>Name:</strong> {{ name }}</p>
                <p><strong>Email:</strong> <a href="mailto:{{ email }}">{{ email }}</a></p>
                <p><strong>Date:</strong> {% now "F j, Y, g:i a" %}</p>
                <p><strong>Message:</strong> {{ message }}</p>
            </div>

            <p>Please respond to this inquiry at your earliest convenience. If you need any additional information, please don't hesitate to review the submitted form or contact the sender directly.</p>

            <p>Best regards,<br><strong>Librainian Team</strong></p>
        </div>

        <div class="footer">
            <p>&copy; {% now "Y" %} {{ library_name }} - Powered by <a href="https://www.librainian.com">Librainian</a>. All rights reserved.</p>

            <div class="social-links">
                <a href="https://www.facebook.com/librainian" aria-label="Facebook">
                    <i class="fab fa-facebook"></i>
                </a>
                <a href="https://twitter.com/librainian_app" aria-label="Twitter">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="https://www.instagram.com/librainian_app" aria-label="Instagram">
                    <i class="fab fa-instagram"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Font Awesome for icons -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>
</html>