from django.urls import path
from . import views


urlpatterns = [
    path("", views.students_data, name="students_data"),
    path("create/", views.student_create, name="create"),
    path("resend_otp/", views.resend_otp, name="resend_otp"),
    path("verify_otp/", views.verify_otp, name="verify_otp"),
    path(
        "create_registration/<slug:slug>/",
        views.create_registration_fee,
        name="create_registration",
    ),
    path(
        "student-registration-temp/<slug:slug>/",
        views.temp_student_form,
        name="student-registration-temp",
    ),
    path("temp_students_list/", views.temp_students_list, name="temp_students_list"),
    path("temp_subLib_students_list/", views.temp_subLib_students_list, name="temp_subLib_students_list"),
    path("create_invoice/<slug:slug>/", views.create_invoice, name="create_invoice"),
    path(
        "invoice_success/<slug:slug>/",
        views.invoice_success,
        name="invoice_success",
    ),
    path(
        "invoice_student/<slug:slug>/",
        views.invoice_student,
        name="invoice_student",
    ),
    path("book-seat/<slug:invoice_slug>", views.book_seat, name="book_seat"),
    path("<slug:slug>/", views.student_data, name="student_details"),
    path("update/<slug:slug>/", views.student_update, name="update"),
    path("doupdate/<slug:slug>/", views.student_doupdate, name="doupdate"),
    path("delete/<slug:slug>/", views.student_delete, name="delete"),
    path("email/<slug:slug>/", views.send_student_email, name="send_student_email"),
    path("<slug:slug>/send-email/", views.send_students_email, name="send_email"),
    path("<slug:slug>/send-sms/", views.send_student_sms, name="send_sms"),
    path(
        "s/<str:short_code>/", views.redirect_to_original, name="redirect_to_original"
    ),
    path("student_active/<pk>", views.student_approval, name="student_active"),
    path("temp_students/update/<int:student_id>/", views.update_temp_student, name="update_temp_student"),
    path("temp_students/delete/<int:student_id>/", views.delete_temp_student, name="delete_temp_student"),

]

handler404 = "Library.views.page_not_found_view"
