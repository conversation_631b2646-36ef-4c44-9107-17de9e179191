<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

    <!-- Primary Meta Tags -->
    <title>{{ librarian.library_name }} QR Code | Student Registration | Librainian</title>
    <meta name="description" content="Access the official QR code for {{ librarian.library_name }} located in {{ librarian.librarian_address }}. Scan to register as a student, access library services, and view detailed information. Download the QR code for easy access to this library's resources.">
    <meta name="keywords" content="{{ librarian.library_name }}, QR code registration, library QR code, student registration QR, {{ librarian.library_name }} registration, library in {{ librarian.librarian_address }}, scan QR code library, digital library access, library management system, Librainian app, student library registration, library services access, {{ librarian.library_name }} student portal, library digital pass, contactless library registration, library membership QR, quick library registration, mobile library access, library check-in QR code">
    <meta name="author" content="Librainian">
    <meta name="robots" content="index, follow, max-image-preview:large">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">
    <link rel="canonical" href="https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/">

    <!-- Browser/Mobile Configuration -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="google" content="notranslate">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="{{ librarian.library_name }} QR Code">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:url" content="https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/">
    <meta property="og:title" content="{{ librarian.library_name }} QR Code | Student Registration">
    <meta property="og:description" content="Access the official QR code for {{ librarian.library_name }} located in {{ librarian.librarian_address }}. Scan to register as a student and access library services.">
    <meta property="og:image" content="https://www.librainian.com/static/img/qr-code-preview.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="{{ librarian.library_name }} QR Code | Student Registration">
    <meta name="twitter:description" content="Access the official QR code for {{ librarian.library_name }}. Scan to register as a student and access library services.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/qr-code-preview.jpg">

    <!-- Structured Data - QR Code -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ImageObject",
      "contentUrl": "data:image/png;base64,{{ qr_code }}",
      "name": "{{ librarian.library_name }} Registration QR Code",
      "description": "QR Code for student registration at {{ librarian.library_name }} in {{ librarian.librarian_address }}",
      "encodingFormat": "image/png",
      "uploadDate": "{% now 'c' %}",
      "copyrightHolder": {
        "@type": "Organization",
        "name": "Librainian",
        "url": "https://www.librainian.com/"
      }
    }
    </script>

    <!-- Structured Data - Library -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "{{ librarian.library_name }}",
      "url": "https://www.librainian.com/librarian/library-details/{{ librarian.slug }}/",
      "logo": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ librarian.librarian_address }}",
        "addressCountry": "IN"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "{{ librarian.librarian_phone_num }}",
        "contactType": "customer service"
      },
      "sameAs": [
        "https://www.librainian.com/",
        "https://www.facebook.com/librainian",
        "https://twitter.com/librainian_app"
      ]
    }
    </script>

    <!-- Breadcrumb Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.librainian.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Libraries",
          "item": "https://www.librainian.com/librarian/library-list/"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "{{ librarian.library_name }}",
          "item": "https://www.librainian.com/librarian/library-details/{{ librarian.slug }}/"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "QR Code",
          "item": "https://www.librainian.com/librarian/qr-code/{{ librarian.slug }}/"
        }
      ]
    }
    </script>

    <!-- Stylesheets -->
    <link rel="apple-touch-icon" href="/static/img/librainian-logo-black-transparent.png">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="manifest" href="/static/js/manifest.json">
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">

    <style>
        /* Base Styles */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --text-color: #1e293b;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --bg-gradient: linear-gradient(135deg, #1e293b, #3b82f6);
            --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.2);
            --border-radius: 15px;
            --transition: all 0.3s ease;
        }

        .qr_body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background: var(--bg-gradient);
            color: var(--text-white);
            font-family: 'Comfortaa', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header Styles */
        header {
            padding-top: 2rem;
        }

        .breadcrumb {
            background-color: transparent !important;
        }

        .breadcrumb-item a {
            text-decoration: none;
            transition: var(--transition);
        }

        .breadcrumb-item a:hover {
            opacity: 0.8;
        }

        /* Main Content Styles */
        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            flex: 1;
            padding: 1rem 0;
        }

        .qr-card {
            margin-top: 1rem;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            max-width: 350px;
            width: 100%;
            background: var(--text-white);
            color: var(--text-color);
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .qr-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .qr-card h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }

        .qr-image-container {
            margin: 1.5rem 0;
            position: relative;
        }

        .qr-card img {
            border: 5px solid var(--primary-color);
            border-radius: 10px;
            max-width: 100%;
            height: auto;
            transition: var(--transition);
        }

        .qr-card img:hover {
            transform: scale(1.02);
        }

        .qr-card p {
            font-size: 0.95rem;
            color: var(--text-light);
            line-height: 1.5;
        }

        .qr-card h2 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
        }

        .qr-card ol {
            text-align: left;
            margin-bottom: 1.5rem;
        }

        .qr-card ol li {
            margin-bottom: 0.5rem;
        }

        /* Button Styles */
        .download-btn {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            background-color: var(--primary-color);
            color: var(--text-white);
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .download-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .download-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        }

        .btn-outline-primary {
            color: var(--text-white);
            border-color: var(--text-white);
            background-color: transparent;
            transition: var(--transition);
        }

        .btn-outline-primary:hover {
            background-color: var(--text-white);
            color: var(--primary-color);
        }

        /* Footer Styles */
        footer {
            margin-top: 2rem;
            width: 100%;
            text-align: center;
            color: var(--text-white);
            font-size: 0.9rem;
            padding-bottom: 1.5rem;
        }

        footer a {
            text-decoration: none;
            transition: var(--transition);
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .qr-card {
                max-width: 320px;
                padding: 1.5rem;
            }

            .qr-card h1 {
                font-size: 1.3rem;
            }

            .download-btn {
                padding: 0.6rem 1.2rem;
                font-size: 0.95rem;
            }
        }

        @media (max-width: 576px) {
            .qr-card {
                max-width: 100%;
                margin: 1rem 0.5rem;
            }

            .qr-card img {
                border-width: 3px;
            }
        }
    </style>
</head>
<body class="qr_body">



    <header class="container pt-4">
        <div class="text-center mb-3">
            <a href="/" aria-label="Return to Librainian homepage">
                <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" width="150" height="auto" class="img-fluid">
            </a>
        </div>
        <nav aria-label="breadcrumb" class="d-none d-md-block">
            <ol class="breadcrumb justify-content-center bg-transparent">
                <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                <li class="breadcrumb-item"><a href="/librarian/library-list/" class="text-white">Libraries</a></li>
                <li class="breadcrumb-item"><a href="/librarian/library-details/{{ librarian.slug }}/" class="text-white">{{ librarian.library_name }}</a></li>
                <li class="breadcrumb-item active text-white-50" aria-current="page">QR Code</li>
            </ol>
        </nav>
    </header>

    <main class="container qr-code-container">
        <section class="qr-card">
            <h1>QR Code for {{ librarian.library_name }}</h1>
            <p class="text-muted mb-3">Use this QR code to register as a student at {{ librarian.library_name }} in {{ librarian.librarian_address }}</p>

            <div class="qr-image-container">
                <img id="qrImage"
                     src="data:image/png;base64,{{ qr_code }}"
                     alt="QR Code for {{ librarian.library_name }} student registration"
                     class="img-fluid"
                     width="250"
                     height="250">
            </div>

            <div class="mt-4 mb-3">
                <h2 class="h5">How to use this QR code:</h2>
                <ol class="text-start small text-muted ps-4">
                    <li>Open your Google Lens app</li>
                    <li>Point it at the QR code above</li>
                    <li>Tap the URL that appears</li>
                    <li>Complete your registration as a student</li>
                </ol>
            </div>

            <button class="download-btn" onclick="downloadQR()" aria-label="Download QR Code">
                <i class="fas fa-download me-2"></i> Download QR Code
            </button>

            <div class="mt-3">
                <a href="/librarian/library-details/{{ librarian.slug }}/" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i> Back to Library Details
                </a>
            </div>
        </section>

        <section class="mt-4 text-center text-white">
            <h2 class="h5">{{ librarian.library_name }}</h2>
            <p class="small mb-1"><i class="fas fa-map-marker-alt me-2"></i>{{ librarian.librarian_address }}</p>
            <p class="small mb-1"><i class="fas fa-phone me-2"></i>{{ librarian.librarian_phone_num }}</p>
            <p class="small"><i class="fas fa-envelope me-2"></i>{{ librarian.user.email }}</p>
        </section>
    </main>

    <footer class="container text-center mt-4 pb-4">
        <div class="small text-white-50">
            <p>&copy; {% now "Y" %} Librainian - The #1 Library Management System. All Rights Reserved.</p>
            <p class="mb-0">
                <a href="/privacy-policy/" class="text-white-50 me-3">Privacy Policy</a>
                <a href="/terms-of-service/" class="text-white-50 me-3">Terms of Service</a>
                <a href="/contact/" class="text-white-50">Contact Us</a>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- QR Code Download Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Focus handling for accessibility
            document.querySelector('.download-btn').focus();

            // Track QR code view in analytics
            if (typeof gtag === 'function') {
                gtag('event', 'view_qr_code', {
                    'event_category': 'Engagement',
                    'event_label': '{{ librarian.library_name }} QR Code',
                    'library_name': '{{ librarian.library_name }}',
                    'library_location': '{{ librarian.librarian_address }}'
                });
            }
        });

        function downloadQR() {
            // Track download event
            if (typeof gtag === 'function') {
                gtag('event', 'download_qr_code', {
                    'event_category': 'Conversion',
                    'event_label': '{{ librarian.library_name }} QR Code Download'
                });
            }

            const qrImage = document.getElementById('qrImage');
            const link = document.createElement('a');
            link.href = qrImage.src;
            link.download = '{{ librarian.library_name|slugify }}_qr_code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
