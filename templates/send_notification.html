<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Notification</title>
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Firebase SDK v8.x.x -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>
</head>
<body style="background-color: #f8f9fa;">

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-dark text-white text-center">
                        <h4>Send Notification</h4>
                        <button id="notificationBtn" class="btn btn-primary">Enable Notifications</button>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="title" class="form-label">Title:</label>
                                <input type="text" class="form-control" id="title" name="title" placeholder="Enter title" required>
                            </div>
                            <div class="mb-3">
                                <label for="body" class="form-label">Body:</label>
                                <textarea class="form-control" id="body" name="body" rows="4" placeholder="Enter notification body" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="user" class="form-label">Select User:</label>
                                <select class="form-select" id="user" name="user">
                                    <option value="">-- Select User --</option>
                                    {% for user in users %}
                                        <option value="{{ user.id }}">{{ user.username }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="sendToAll" name="send_to_all">
                                <label class="form-check-label" for="sendToAll">
                                    Send to all users
                                </label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-dark btn-block">Send Notification</button>
                            </div>
                        </form>
                    </div>
                    {% if messages %}
                        <div class="card-footer">
                            <ul class="list-group">
                                {% for message in messages %}
                                    <li class="list-group-item">{{ message }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Automatically request notification permission and register device token
        document.addEventListener('DOMContentLoaded', function() {
            const notificationBtn = document.getElementById('notificationBtn');
            notificationBtn.style.display = 'none'; // Hide the button since we will auto-register

            if ('Notification' in window) {
                requestAndShowNotification();
            } else {
                console.log('This browser does not support notifications.');
            }
        });

        function requestAndShowNotification() {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    showNotification();
                    registerDeviceToken();
                } else {
                    console.log('Notification permission denied');
                }
            });
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            messaging.getToken({ vapidKey: 'BGX2t3YXGQdWxsYD_V3WpvlzCvwS7Ob0b9oGq0Thsg_OLHs22urXWd_CKv4QBdBVJNkZJJHq-QG8zB2p4qPYdrM' }).then((currentToken) => {
                if (currentToken) {
                    const deviceType = 'web'; // Change this based on the actual device type
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    console.log('No registration token available. Request permission to generate one.');
                }
            }).catch((err) => {
                console.log('An error occurred while retrieving token. ', err);
            });
        }

        function saveDeviceToken(token, deviceType) {
            fetch('/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken') // Get CSRF token for security
                },
                body: `token=${token}&device_type=${deviceType}`
            })
            .then(response => response.json())
            .then(data => {
                console.log(data.message);
                console.log('Device Token:', token); // Log the device token to the console
            })
            .catch(error => {
                console.error('Error saving device token:', error);
            });
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>

    <script>
        // Register the service worker
        if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(function(registration) {
            console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(function(error) {
            console.log('Service Worker registration failed:', error);
            });
        }

    </script>

</body>
</html>
