
<style>
    /* Sidebar Core Styles */
    .sidebar {
        position: fixed;
        top: 0.5rem;
        left: 0.5rem;
        height: 97vh;
        width: 250px;
        padding-top: 20px;
        margin-bottom: 3rem;
        transition: all 0.3s ease;
        border-radius: 1rem;
        background-color: #28345a;
        color: #fff;
        /* Unified scrollbar handling */
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        z-index: 1010;
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    /* Dashboard heading */
    .dashboard_heading {
        padding: 0.5rem 1rem;
        margin: 0rem 1rem 1rem 1rem;
        border-radius: 1rem;
        background: #6a9a9e;
        max-width: 13rem;
        font-weight: bold;
    }

    /* Sidebar links */
    .sidebar a {
        color: #fff;
        padding: 12px 15px 12px 25px;
        display: block;
        text-decoration: none;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
        margin-bottom: 2px;
    }

    .sidebar a:hover {
        background-color: rgba(73, 80, 87, 0.7);
        border-left: 3px solid #007bff;
    }

    .sidebar a.active {
        background-color: #9bc6bf;
        color: white;
        border-left: 3px solid #007bff;
    }

    /* Main content adjustment */
    .main-content {
        margin-left: 250px;
        padding: 4px;
        transition: all 0.3s;
    }

    /* Navbar styling */
    .navbar {
        margin: 0.5rem;
        position: sticky;
        top: 0.5rem;
        border-radius: 1rem;
        background: #fff !important;
        box-shadow: 0px 4px 6px 1px rgba(40, 52, 90, 0.3);
        z-index: 1000;
    }

    .navbar-brand {
        margin: 0 !important;
    }

    /* Mobile adjustments */
    @media (max-width: 768px) {
        .sidebar {
            display: none; /* Hidden on mobile */
        }

        .main-content {
            margin-left: 0;
        }

        .nav_small {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    /* Footer Menu for Mobile */
    .footer-menu {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        background-color: #f1f8ff;
        padding: 10px 0;
        z-index: 1000;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }

    .footer-menu a,
    .footer-menu .settings-link {
        color: #000000;
        font-size: 24px;
        text-align: center;
        text-decoration: none;
        position: relative;
    }

    .footer-menu a.active i,
    .footer-menu .settings-link.active i {
        color: #28345a;
    }

    /* Add animation for menu icon */
    .settings-link i.fa-bars {
        transition: transform 0.3s ease;
    }

    .settings-link.active i.fa-bars {
        transform: rotate(90deg);
    }

    /* Mobile submenu */
    .submenu {
        display: none;
        position: absolute;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(192, 221, 253, 0.8);
        z-index: 1001;
        left: 0;
        bottom: 60px;
        padding: 15px;
        border-radius: 10px;
        width: 280px;
        max-height: 400px;
        overflow-y: auto;
        transition: opacity 0.2s ease;
        opacity: 0;
        pointer-events: none;
    }

    /* Show submenu when open class is applied */
    .submenu.open {
        display: block;
        opacity: 1;
        pointer-events: auto;
    }

    .footer-menu .settings-link .submenu {
        left: -230px;
    }

    .footer-menu .settings-link a {
        display: block;
        color: #000000;
        padding: 8px 12px;
        text-decoration: none;
        text-align: start;
        margin-top: 5px;
        font-size: 0.8rem;
        border-radius: 5px;
    }

    .footer-menu .settings-link a:hover {
        background-color: #f0f0f0;
    }

    /* Icon text wrapper for footer menu */
    .icon-text-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .icon-text-wrapper i {
        font-size: 24px;
        margin-bottom: 5px;
    }

    .dashboard-text {
        font-size: 12px;
        line-height: 1.2;
    }

    /* Modal styling */
    .modal-content {
        border-radius: 1rem;
    }

    .btn-primary, .btn-secondary, .btn-success, .btn-danger {
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    /* Footer adjustments for mobile */
    @media (max-width: 768px) {
        .footer {
            margin-bottom: 100px;
        }
    }

    /* Profile dropdown styling */
    .profile-dropdown {
        width: 280px;
        max-width: 90vw;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border: none;
        right: 0;
        left: auto !important;
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    /* Ensure dropdown is positioned correctly */
    .dropdown-menu-end {
        right: 0;
        left: auto !important;
    }

    .profile-dropdown .dropdown-item {
        border-radius: 5px;
        transition: all 0.2s ease;
        white-space: normal;
        padding: 0.5rem;
    }

    .profile-dropdown .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .profile-dropdown .dropdown-item.text-danger:hover {
        background-color: #f8d7da;
    }

    .nav-item .nav-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        transition: all 0.2s ease;
    }

    /* Profile image styling */
    .profile-img {
        width: 45px;
        height: 45px;
        object-fit: cover;
        border-radius: 50%;
        border: 2px solid #f1f1f1;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    /* Responsive adjustments for profile dropdown */
    @media (max-width: 576px) {
        .profile-dropdown {
            width: 250px;
            position: fixed !important;
            top: auto !important;
            bottom: 60px;
            right: 10px !important;
            left: auto !important;
        }

        .profile-img {
            width: 40px;
            height: 40px;
        }
    }
</style>


<div class="sidebar">
    <h6 class="text-center dashboard_heading">{{ role|capfirst }} Dashboard</h6>

    {% if role == "librarian" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/analytics/" onclick="setActiveLink(this)"><i class="fas fa-chart-line"></i> Analytics</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/students/temp_students_list/" onclick="setActiveLink(this)"><i class="fas fa-book-reader"></i> Pending Students</a>
    <a href="/students/" onclick="setActiveLink(this)"><i class="fa-solid fa-users"></i> Students Section</a>
    <a href="/{{role}}/marketing/" onclick="setActiveLink(this)"><i class="bi bi-megaphone-fill"></i> Marketing Section</a>
    <a href="/visitors/" onclick="setActiveLink(this)"><i class="fa-solid fa-user"></i> Visitors Section</a>
    <a href="/{{role}}/daily-transaction/" onclick="setActiveLink(this)"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
    <a href="/sublibrarian/signup/" onclick="setActiveLink(this)"><i class="fa-solid fa-user-plus"></i> Add Sublibrarian</a>
    <a href="/{{role}}/shifts/" onclick="setActiveLink(this)"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
    <a href="/{{role}}/seats/" onclick="setActiveLink(this)"><i class="fa-solid fa-chair"></i> Seats Control</a>
    <a href="/membership/plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
    <a href="/membership/sms-plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

    {% elif role == "sublibrarian" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/students/" onclick="setActiveLink(this)"><i class="fa-solid fa-users"></i> Students Section</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/students/temp_subLib_students_list/" onclick="setActiveLink(this)"><i class="fas fa-book-reader"></i> Pending Students</a>
    <a href="/{{role}}/daily-transaction/" onclick="setActiveLink(this)"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
    <a href="/membership/plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
    <a href="/membership/sms-plans/" onclick="setActiveLink(this)"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
    <a href="/visitors/" onclick="setActiveLink(this)"><i class="fa-solid fa-user"></i> Visitors Section</a>
    <a href="/{{role}}/shifts/" onclick="setActiveLink(this)"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
    <a href="/{{role}}/seats/" onclick="setActiveLink(this)"><i class="fa-solid fa-chair"></i> Seats Control</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

    {% elif role == "manager" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>

    {% elif role == "librarycommander" %}
    <a href="/{{role}}/dashboard/" onclick="setActiveLink(this)"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="/{{role}}/table/" onclick="setActiveLink(this)"><i class="fa fa-table"></i> Table</a>
    <a href="/blogs/coupons/create_coupon/" onclick="setActiveLink(this)"><i class="fa fa-ticket-alt"></i> Coupon</a>
    <a href="/blogs/" onclick="setActiveLink(this)"><i class="fa fa-pencil-alt"></i> Blog Section</a>
    <a href="/{{role}}/complaint_dashboard/" onclick="setActiveLink(this)"><i class="fa fa-exclamation-circle"></i> Complaint Section</a>
    <a href="/{{role}}/logs/" onclick="setActiveLink(this)"><i class="fa fa-file-alt"></i> System Logs</a>
    <a href="/{{role}}/backups/" onclick="setActiveLink(this)"><i class="fa fa-download"></i> Download Database</a>
    <a href="/{{role}}/restore/" onclick="setActiveLink(this)"><i class="fa fa-undo"></i> Restore Database</a>
    <a href="/{{role}}/help/" onclick="setActiveLink(this)"><i class="bi bi-question-octagon-fill"></i> Help</a>
    <a href="/{{role}}/feedback/" onclick="setActiveLink(this)"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>
    {% endif %}


    <!-- Logout button with modal trigger -->
    <a data-bs-toggle="modal" data-bs-target="#logoutModal"><i class="fas fa-sign-out-alt"></i> Logout</a>
</div>

<script>
    // Function to set active class for sidebar links
    function setActiveLink(link) {
        // Remove active class from all sidebar links
        let sidebarLinks = document.querySelectorAll('.sidebar a');
        sidebarLinks.forEach(sidebarLink => {
            sidebarLink.classList.remove('active');
        });

        // Add active class to clicked link
        link.classList.add('active');

        // Store the active link in localStorage
        localStorage.setItem('activeLink', link.getAttribute('href'));
    }
</script>


    <!-- Logout Modal -->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="logoutModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logoutModalLabel">Logout Confirmation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to logout?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                    <a href="/{{role}}/logout" class="btn btn-primary">Yes</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Menu -->
    <div class="footer-menu d-md-none">
    {% if role == "sublibrarian"%}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/students/create/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-circle-plus"></i>
                <span class="dashboard-text"><strong>Student Create</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>

        <a href="/visitors/create" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-user"></i>
                <span class="dashboard-text"><strong>Visitor Create</strong></span>
            </div>
        </a>
    {% elif role == "manager" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>
    {% elif role == "librarian" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/analytics/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-chart-line"></i>
                <span class="dashboard-text"><strong>Analytics</strong></span>
            </div>
        </a>

        <a href="/students/create/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-circle-plus"></i>
                <span class="dashboard-text"><strong>Student</strong></span>
            </div>
        </a>

        <a href="/students/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-users"></i>
                <span class="dashboard-text"><strong>Students</strong></span>
            </div>
        </a>

    {% elif role == "librarycommander" %}
        <a href="/{{role}}/dashboard/" class="footer-link footer-dashboard">
            <div class="icon-text-wrapper">
                <i class="fas fa-tachometer-alt"></i>
                <span class="dashboard-text"><strong>Dashboard</strong></span>
            </div>
        </a>

        <a href="/{{role}}/table/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-table"></i>
                <span class="dashboard-text"><strong>Table</strong></span>
            </div>
        </a>

        <a href="/blogs/create-blog/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fas fa-pencil-alt"></i>
                <span class="dashboard-text"><strong>Blogs</strong></span>
            </div>
        </a>

        <a href="/{{role}}/complaint_dashboard/" class="footer-link">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-exclamation-circle"></i>
                <span class="dashboard-text"><strong>Complaint</strong></span>
            </div>
        </a>
    {% endif %}

        <!-- Options menu with submenu -->
        <div class="settings-link footer-link" id="footer-settings">
            <div class="icon-text-wrapper">
                <i class="fa-solid fa-bars"></i>
                <span class="dashboard-text"><strong>Options</strong></span>
            </div>

            <div class="submenu" id="submenu">
                {% if role == "librarian" %}
                    <a href="/{{role}}/profile/"><i class="fas fa-user"></i> View Profile</a>
                    <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                    <a href="/students/temp_students_list/"><i class="fas fa-book-reader"></i> Pending Students</a>
                    <a href="/{{role}}/marketing/"><i class="bi bi-megaphone-fill"></i> Marketing Section</a>
                    <a href="/visitors/"><i class="fa-solid fa-user"></i> Visitors Section</a>
                    <a href="/{{role}}/daily-transaction/"><i class="fa-solid fa-money-bill"></i> Daily Transactions</a>
                    <a href="/sublibrarian/signup/"><i class="fa-solid fa-user-plus"></i> Add Sub-librarian</a>
                    <a href="/{{role}}/shifts/"><i class="fa-solid fa-table-list"></i> Manage Shifts</a>
                    <a href="/{{role}}/seats/"><i class="fa-solid fa-chair"></i> Seats Control</a>
                    <a href="/membership/plans/"><i class="fa-solid fa-cart-shopping"></i> Membership</a>
                    <a href="/membership/sms-plans/"><i class="fa-solid fa-mobile-screen-button"></i> Sms Pack</a>
                    <a href="/{{role}}/help/"><i class="bi bi-question-octagon-fill"></i> Help</a>
                    <a href="/{{role}}/feedback/"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>

                {% elif role == "sublibrarian" %}
                    <a href="/students/"><i class="fa-solid fa-users"></i> Students Section</a>
                    <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                    <a href="/visitors/"><i class="fa-solid fa-user"></i> Visitors Section</a>

                {% elif role == "manager" %}
                    <!-- Manager specific menu items can be added here -->

                {% elif role == "librarycommander" %}
                    <a href="/{{role}}/dashboard/"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    <a href="/{{role}}/table/"><i class="fa fa-table"></i> Table</a>
                    <a href="/blogs/"><i class="fa fa-pencil-alt"></i> Blog Section</a>
                    <a href="/{{role}}/complaint_dashboard/"><i class="fa fa-exclamation-circle"></i> Complaint Section</a>
                    <a href="/{{role}}/logs/"><i class="fa fa-file-alt"></i> System Logs</a>
                    <a href="/{{role}}/backups/"><i class="fa fa-download"></i> Download Database</a>
                    <a href="/{{role}}/restore/"><i class="fa fa-undo"></i> Restore Database</a>
                    <a href="/{{role}}/help/"><i class="bi bi-question-octagon-fill"></i> Help</a>
                    <a href="/{{role}}/feedback/"><i class="bi bi-chat-left-text-fill"></i> Feedbacks</a>
                {% endif %}

                <!-- Logout link with modal trigger -->
                <a data-bs-toggle="modal" data-bs-target="#logoutModal"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </div>



    <!-- Main content -->
    <div class="main-content">
        <!-- Navbar -->

        <nav class="navbar nav_small navbar-expand-lg navbar-light fixed-top">
            {% if role == "librarian" or role == "sublibrarian" %}
                <a class="navbar-brand" href="#">
                    <img src="{% if request.user.librarian_param.image  %}{{ request.user.librarian_param.image.url }}{% else %}https://picsum.photos/150{% endif %}"
                            alt="avatar" style="height: 50px; width: 250px;" loading="lazy">
                </a>

            {% else %}
                <a class="navbar-brand" href="#">
                    <img src="/static/img/link_cover.jpg" alt="Default Logo" style="width: 220px;" loading="lazy">
                </a>
            {% endif %}

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <!-- Messages Dropdown -->
                    <!-- <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="messagesDropdown" role="button" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-envelope fa-2x"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="messagesDropdown">
                            <a class="dropdown-item" href="#">Message 1</a>
                            <a class="dropdown-item" href="#">Message 2</a>
                            <a class="dropdown-item" href="#">Message 3</a>
                        </div>
                    </li> -->

                    <!-- Notifications Dropdown -->
                    <!-- <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="notificationsDropdown" role="button" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-bell fa-2x"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="notificationsDropdown">
                            <a class="dropdown-item" href="#">Notification 1</a>
                            <a class="dropdown-item" href="#">Notification 2</a>
                            <a class="dropdown-item" href="#">Notification 3</a>
                        </div>
                    </li> -->

                    <!-- Profile Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link d-flex align-items-center" id="profileDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true"
                        aria-expanded="false">
                            <img src="/static/img/admin.png" alt="Profile" style="height: 35px; width: 35px; margin-right: 8px;" loading="lazy">
                            <span>Profile</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end profile-dropdown" aria-labelledby="profileDropdown">
                            <div class="px-2 py-2">
                                <div class="d-flex align-items-center mb-2">
                                    <img src="/static/img/admin.png" alt="Profile Image" class="profile-img me-2" loading="lazy">
                                    <div style="font-size: 0.9rem;">
                                        <h6 class="mb-0">{{user.first_name}} {{user.last_name}}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt"></i> <span id="date"></span>
                                        </small>
                                    </div>
                                </div>

                                <div class="list-group list-group-flush mb-2">
                                    <a href="/{{role}}/profile/" class="list-group-item list-group-item-action border-0 py-1 px-2">
                                        <i class="fas fa-user me-2"></i> View Profile
                                    </a>
                                    <a href="/{{role}}/edit-profile/" class="list-group-item list-group-item-action border-0 py-1 px-2">
                                        <i class="fas fa-edit me-2"></i> Edit Profile
                                    </a>
                                </div>

                                <div class="d-grid gap-2">
                                    <a data-bs-toggle="modal" data-bs-target="#logoutModal" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Bootstrap Bundle with Popper (includes all Bootstrap JS) -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

        <!-- Bootstrap initialization check -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('Bootstrap version in sidebar:', bootstrap.Collapse.VERSION);

                // Check if navbar toggler exists
                const navbarToggler = document.querySelector('.navbar-toggler');
                if (navbarToggler) {
                    console.log('Navbar toggler found in sidebar:', navbarToggler);

                    // Add a click event listener to log when it's clicked
                    navbarToggler.addEventListener('click', function() {
                        console.log('Navbar toggler clicked in sidebar');
                    });
                } else {
                    console.error('Navbar toggler not found in sidebar');
                }
            });
        </script>



        <script>
            function updateDateTime() {
                const now = new Date();

                // Format date as dd/mm/yy
                const day = String(now.getDate()).padStart(2, '0');
                const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                const year = String(now.getFullYear()).slice(-2); // Get the last 2 digits of the year
                const date = `${day}/${month}/${year}`;

                // Update the date element
                const dateElement = document.getElementById('date');
                if (dateElement) {
                    dateElement.textContent = date;
                }
            }

            document.addEventListener('DOMContentLoaded', () => {
                updateDateTime();
                // Update every minute
                setInterval(updateDateTime, 60000);
            });
        </script>


        <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Set active link in footer menu based on current path
        const currentPath = window.location.pathname;
        const footerLinks = document.querySelectorAll('.footer-link');

        footerLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }

            // Add click event to each footer link
            link.addEventListener('click', function() {
                // Remove active class from all links
                footerLinks.forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                this.classList.add('active');
            });
        });

        // Handle the options menu toggle
        const settingsLink = document.getElementById('footer-settings');
        const submenu = document.getElementById('submenu');

        if (settingsLink && submenu) {
            // Track if the menu is open
            let isMenuOpen = false;

            settingsLink.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent event from bubbling up

                // Toggle menu state
                isMenuOpen = !isMenuOpen;

                // Apply or remove the open class based on state
                if (isMenuOpen) {
                    submenu.classList.add('open');
                    footerLinks.forEach(link => link.classList.remove('active'));
                    settingsLink.classList.add('active');
                } else {
                    submenu.classList.remove('open');
                    settingsLink.classList.remove('active');
                }
            });

            // Close submenu when clicking outside
            document.addEventListener('click', function(event) {
                if (isMenuOpen && !settingsLink.contains(event.target) && !submenu.contains(event.target)) {
                    submenu.classList.remove('open');
                    settingsLink.classList.remove('active');
                    isMenuOpen = false;
                }
            });

            // Prevent submenu clicks from closing the menu
            submenu.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Close menu when clicking a submenu item
            submenu.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', function() {
                    // Don't close for modal triggers
                    if (!this.hasAttribute('data-bs-toggle')) {
                        submenu.classList.remove('open');
                        settingsLink.classList.remove('active');
                        isMenuOpen = false;
                    }
                });
            });
        }

        // Set active link in sidebar based on localStorage or current path
        const sidebarLinks = document.querySelectorAll('.sidebar a');
        const savedActiveLink = localStorage.getItem('activeLink');

        if (savedActiveLink) {
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === savedActiveLink) {
                    link.classList.add('active');
                }
            });
        } else {
            // If no saved link, set active based on current path
            sidebarLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        }
    });
</script>
