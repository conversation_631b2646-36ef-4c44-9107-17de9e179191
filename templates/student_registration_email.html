<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <title>Student Registration</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        background-color: #f4f4f4;
        font-family: 'Comfortaa', sans-serif !important;
        background-color: #9bc6bf;
      }
      .container {
        max-width: 600px;
        margin: 20px auto;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 1rem;
      }
      .heading {
        text-align: center;
        color: #ffffff;
        background-color: #294282;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      table,
      th,
      td {
        border: 1px solid #ddd;
      }
      th,
      td {
        padding: 10px;
        text-align: left;
      }
      th {
        background-color: #f2f2f2;
      }
      .footer {
        text-align: center;
        font-size: 12px;
        color: #888;
        margin-top: 20px;
      }
      @media (max-width: 768px) {
      .heading{
        font-size: 1.2rem;
      }
      body{
        margin: 1rem;
      }
      .container{
        max-width: 500px;
      }
      .para{
        font-size: 0.8rem;
      }
      }
    </style>
  </head>
  <body> 
    <div class="container">
      <h2 class="p-md-3 p-3 heading" style="border-radius: 1rem;">Student Registration</h2>
      <p class="para">
        A new student registration form has been submitted. Below are the
        details:
      </p>
      <table>
        <tr>
          <th>Name</th>
          <td>{{ student.name }}</td>
        </tr>
        <tr>
          <th>Father's Name</th>
          <td>{{ student.f_name }}</td>
        </tr>
        <tr>
          <th>Age</th>
          <td>{{ student.age }}</td>
        </tr>
        <tr>
          <th>Gender</th>
          <td>{{ student.gender }}</td>
        </tr>
        <tr>
          <th>Email</th>
          <td>{{ student.email }}</td>
        </tr>
        <tr>
          <th>Mobile</th>
          <td>{{ student.mobile }}</td>
        </tr>
        <tr>
          <th>Locality</th>
          <td>{{ student.locality }}</td>
        </tr>
        <tr>
          <th>City</th>
          <td>{{ student.city }}</td>
        </tr>
        <tr>
          <th>State</th>
          <td>{{ student.state.name }}</td>
        </tr>
        <tr>
          <th>Course</th>
          <td>{{ student.course.name }}</td>
        </tr>
      </table>
      <p class="footer">Thank you for managing the registrations!</p>
    </div>
  </body>
</html>
