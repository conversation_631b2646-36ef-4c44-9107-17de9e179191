from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from .views import *
from .user_auth import *
from django.contrib.sitemaps.views import sitemap
from blogs.sitemap import PostSitemap
from librarian.sitemap import PostSitemaps, LibrarianStaticViewSitemap
from subLibrarian.sitemap import StaticViewSitemap
from .sitemap import IndexSitemaps
from .views import robots_txt
from django.views.generic.base import RedirectView
from django.views.generic import TemplateView



favicon_view = RedirectView.as_view(
    url="/static/img/librainian-logo-black-transparent copy.png",
    permanent=True,
)

sitemaps = {
    "posts": PostSitemap,
    "library": PostSitemaps,
    "static": StaticViewSitemap,
    "librarian_static": LibrarianStaticViewSitemap,
    "index": IndexSitemaps,
}


urlpatterns = [
    path("superadmin/", admin.site.urls),
    path("", home, name="home"),
    path("my_temp",TemplateView.as_view(template_name="my_temp.html")),
    path("robots.txt", robots_txt),
    path("about/", about, name="about"),
    path("contact/", contact, name="contact"),
    path("services/", services, name="services"),
    path("download-app/", qrcode_redirect, name="download-app"),
    path("manager/", include("manager.urls")),
    path("librarian/", include("librarian.urls")),
    path("accounts/", include("allauth.urls")),
    path("sublibrarian/", include("subLibrarian.urls")),
    path("librarycommander/", include("libraryCommander.urls")),
    path("blogs/", include("blogs.urls")),
    path("visitors/", include("visitorsData.urls")),
    path("students/", include("studentsData.urls")),
    path("membership/", include("membership.urls")),
    path("ads/", include("advertisements.urls")),
    path("ad-investor/", include("adInvestor.urls")),
    path("wallet/", include("wallet_and_transactions.urls")),
    path("password-reset/", password_reset_request, name="password_reset_request"),
    path("otp-verification/", otp_verification, name="otp_verification"),
    path("sms/", send_sms, name="send_welcome_sms"),
    re_path(r"^favicon\.ico$", favicon_view),
    path(
        "sitemap.xml",
        sitemap,
        {"sitemaps": sitemaps},
        name="django.contrib.sitemaps.views.sitemap",
    ),
    path('firebase-messaging-sw.js', TemplateView.as_view(
        template_name="firebase-messaging-sw.js",
        content_type='application/javascript',
    )),

]

handler404 = "Library.views.page_not_found_view"

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


urlpatterns += staticfiles_urlpatterns()
