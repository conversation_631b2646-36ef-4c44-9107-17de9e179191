<!DOCTYPE html>
<html lang="en">

<head>
  <script>
    // Inline loader script to show loader immediately when page starts loading
    (function() {
        // Create loader HTML with FSEX300 font and LIBRAINIAN text
        var loaderHTML = `
        <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                <style>
                    @font-face {
                        font-family: 'FSEX300';
                        src: url('/static/fonts/FSEX300.ttf') format('truetype');
                        font-weight: normal;
                        font-style: normal;
                    }

                    @keyframes blink {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0; }
                    }

                    @keyframes dots {
                        0% { content: ""; }
                        25% { content: "."; }
                        50% { content: ".."; }
                        75% { content: "..."; }
                        100% { content: ""; }
                    }

                    .loader-text {
                        font-family: 'FSEX300', monospace;
                        font-size: 32px;
                        color: #ffffff;
                        letter-spacing: 2px;
                        margin-bottom: 20px;
                        text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                    }

                    .loader-dots::after {
                        content: "";
                        animation: dots 1.5s infinite;
                    }

                    .loader-bar {
                        width: 300px;
                        height: 20px;
                        background-color: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                        overflow: hidden;
                        margin: 20px auto;
                    }

                    .loader-progress {
                        width: 0%;
                        height: 100%;
                        background-color: #6200ee;
                        border-radius: 10px;
                        animation: progress 2s infinite;
                    }

                    @keyframes progress {
                        0% { width: 0%; }
                        50% { width: 100%; }
                        100% { width: 0%; }
                    }
                </style>
                <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                <div class="loader-bar">
                    <div class="loader-progress"></div>
                </div>
            </div>
        </div>
        `;

        // Add loader to page
        document.write(loaderHTML);

        // Remove loader when page is loaded
        window.addEventListener('load', function() {
            var loader = document.getElementById('initialLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        });
    })();
</script>




  {% load socialaccount %}
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta http-equiv="content-language" content="en">
  <meta name="geo.region" content="IN">

  <!-- SEO Meta Tags -->
  <meta name="robots" content="index,follow,max-image-preview:large">
  <link rel="canonical" href="https://www.librainian.com/librarian/login/">
  <meta name="keywords" content="library login, librarian login, library management system, library software, library app, library account access, secure library login, digital library, library automation, library database">
  <meta name="description" content="Access your Librainian account - The #1 Library Management System. Secure login for librarians to manage resources, track expenses, and streamline operations.">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Librainian">
  <meta property="og:title" content="Login to Librainian - Modern Library Management System">
  <meta property="og:description" content="Access your library's resources with our secure login. Manage your library effectively with Librainian - used by 600+ libraries.">
  <meta property="og:url" content="https://www.librainian.com/librarian/login/">
  <meta property="og:image" content="https://www.librainian.com/static/img/modern_library_digital.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@librainian_app">
  <meta name="twitter:creator" content="@librainian_app">
  <meta name="twitter:title" content="Login to Librainian - Modern Library Management System">
  <meta name="twitter:description" content="Access your library's resources with our secure login. Manage your library effectively with Librainian.">
  <meta name="twitter:image" content="https://www.librainian.com/static/img/modern_library_digital.jpg">

  <!-- Author and Date Info -->
  <meta itemprop="author" content="Librainian Team">
  <meta itemprop="datePublished" content="2024-01-01">
  <meta itemprop="dateModified" content="2024-07-07">

  <!-- Mobile App Capability -->
  <meta name="theme-color" content="#042299">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Librainian">

  <title>Login to Librainian | Modern Library Management System</title>

  <!-- Stylesheets -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ENjdO4Dr2bkBIFxQpeoY8N38y+6HrDgMX8yLvMw8e4jUHlS/z2htRrad2KbnI3eB" crossorigin="anonymous"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GN5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Tiny5&display=swap" rel="stylesheet">
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">

  <!-- Structured Data / JSON-LD -->
  <script type="application/ld+json">
  {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Login to Librainian | Modern Library Management System",
      "description": "Access your Librainian account - The #1 Library Management System. Secure login for librarians to manage resources, track expenses, and streamline operations.",
      "url": "https://www.librainian.com/librarian/login/",
      "publisher": {
          "@type": "Organization",
          "name": "Librainian",
          "logo": {
              "@type": "ImageObject",
              "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
          }
      },
      "isPartOf": {
          "@type": "WebSite",
          "name": "Librainian",
          "url": "https://www.librainian.com/"
      },
      "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
              {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://www.librainian.com/"
              },
              {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Login",
                  "item": "https://www.librainian.com/librarian/login/"
              }
          ]
      }
  }
  </script>

  <!-- Login Form Schema Markup -->
  <script type="application/ld+json">
  {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Librainian Login Form",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web",
      "description": "Login form for Librainian - The #1 Library Management System",
      "potentialAction": {
          "@type": "LoginAction",
          "target": {
              "@type": "EntryPoint",
              "urlTemplate": "https://www.librainian.com/librarian/login/",
              "inLanguage": "en-US",
              "actionPlatform": [
                  "http://schema.org/DesktopWebPlatform",
                  "http://schema.org/MobileWebPlatform"
              ]
          },
          "result": {
              "@type": "UserAccount"
          }
      }
  }
  </script>




  <style>

      body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
        }

    .bg-image {
      background-image: url('/static/img/modern_library_digital.jpg');
      filter: blur(8px);
      -webkit-filter: blur(8px);
      height: 100%;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      position: fixed;
      width: 100%;
      z-index: -1;
    }

    .container {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      padding: 20px;
    }

    .card {
      width: 100%;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .btn-dark a {
      text-decoration: none;
    }

    .gbtn {
      border: 1px solid #93b7ff;
      border-radius: 4px;
      background-color: #f0f0f0;
      padding: 6px 14px; /* Adjusted padding for smaller buttons */
      cursor: pointer;
      font-size: 18px; /* Smaller font size */
      color: black;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }

    .gbtn img {
      width: 28px; /* Adjusted icon size */
      height: 28px;
      margin-right: 1rem;
      vertical-align: middle;
      background-color: rgb(255, 255, 255);
      border-radius: 50%;
      padding: 3px;
    }

    .gbtn:focus {
      outline: 2px solid #8ab5ff;
      outline-offset: 4px;
    }

    .gbtn:hover {
      background-color: #3f83f1;
      border: 1px solid #0a1d44ad;
      color: white;
      transition: 1s;
    }

    .btn-group-inline {
      display: flex;
      gap: 8px; /* Adjusted gap */
      justify-content: space-between;
    }

    a {
      color: #393f81;
      text-decoration: none;
      transition: color 0.3s, text-decoration 0.3s;
      font-size: 14px; /* Reduced font size for links */
    }

    a:hover {
      color: #1a237e; /* Increased contrast on hover */
      text-decoration: none; /* Removed underline */
    }

    .form-control-lg {
      font-size: 14px; /* Reduced font size for form inputs */
      padding: 0.5rem; /* Adjusted padding */
    }

    .form-outline {
      margin-bottom: 1rem;
    }

    @media only screen and (min-width: 767px) {
      .card {
        padding-right: 2rem;
      }
    }

    @media only screen and (min-width: 1023px) {
      .card {
        width: 70%;
        padding-right: 2rem;
      }
    }

  </style>

</head>

<body>

  <div class="bg-image"></div>
  <section class="vh-100">
    <div class="container">
      <div class="card">
        <div class="row">
          <div class="col-md-6 d-none d-md-block">
            <img src="/static/img/modern_library_digital.jpg" alt="login form" loading="lazy" class="img-fluid"
              style="border-radius: 1rem 0 0 1rem; height: 100%;" />
          </div>
          <div class="col-md-6 p-0 d-flex align-items-center">
            <div class="card-body p-4 p-lg-3 text-black">
              <div class="d-flex justify-content-center mb-3">
                <span class="h1 fw-bold mb-0" style="font-family: 'Tiny5', sans-serif; letter-spacing: 5px;">LIBRAINIAN</span>
              </div>

              <h5 class="fw-normal mb-3 pb-3 text-center" style="letter-spacing: 1px;">Sign into your account</h5>
              {% if messages %}
                {% for message in messages %}
                  <div class="alert {% if message.tags == 'success' %}alert-success{% else %}alert-danger{% endif %} alert-dismissible fade show" role="alert">
                    {{ message }}
                  </div>
                {% endfor %}
              {% endif %}

              <form method="post">
                {% csrf_token %}
                <div data-mdb-input-init class="form-outline mb-4">
                  <input type="text" id="form2Example17" class="form-control form-control-lg" placeholder="Username*" name="username" required
                    pattern="^[a-zA-Z0-9@._-]+$" title="Username can only contain letters, numbers, and @._-" />
                </div>

                <div class="input-group mb-4">
                  <input type="password" id="form3Example9" placeholder="Password*" class="form-control form-control-lg" name="password">
                  <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                      Remember me
                    </label>
                  </div>
                </div>

                <div class="btn btn-dark btn-lg w-100 w-md-auto">
                  <button data-mdb-button-init data-mdb-ripple-init class="btn btn-dark btn-lg px-5" type="submit" style="font-size: 1rem;">
                    Login
                  </button>
                  {% if role == "librarian" %}
                  <!-- <a href="/sublibrarian/login/" style="color: #f3f3f7;">
                    <button data-mdb-button-init data-mdb-ripple-init class="btn btn-dark btn-lg w-100" style="font-size: 1rem;" type="button">
                      Sublibrarian Login
                    </button>
                  </a> -->
                {% endif %}
                </div>

                {% if role == "librarian" %}
                  <!-- <div class="pt-1 mb-3">
                    <a href="{% url 'google_login' %}" class="gbtn">
                      <img src="/static/img/figma_files/google_g.png" alt="Google logo">
                      Sign in with Google
                    </a>
                  </div>  -->
                {% endif %}

              </form>
              <div class="d-flex justify-content-between pt-3">
                <a class="small text-muted" href="/">Back to Home</a><br>

                <a class="small text-muted" href="/password-reset/">Forgot password?</a>
              </div>

              {% if role == "librarian" %}
              <p class="mb-0 pb-lg-2 mt-3" style="color: #393f81;">Don't have an account? <a
                href="/{{role}}/signup/" style="color: #393f81;">Register here</a>
              </p>
              {% endif %}
              <a href="https://www.librainian.com/blogs/p/privacy-policy/" class="small text-muted">Terms of use.</a>
                <a href="https://www.librainian.com/blogs/p/terms-of-use/" class="small text-muted">Privacy policy</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var alerts = document.querySelectorAll('.alert');
      alerts.forEach(function(alert) {
        setTimeout(function() {
          alert.style.transition = 'opacity 1s';
          alert.style.opacity = '0';
          setTimeout(function() {
            alert.style.display = 'none';
          }, 1000);
        }, 5000);
      });
    });
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const togglePassword = document.querySelector('#togglePassword');
      const password = document.querySelector('#form3Example9');

      togglePassword.addEventListener('click', function () {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye-slash');
      });
    });
  </script>
</body>
<script>
  window.addEventListener("load", function () {
      const path = window.location.pathname; // Get current page path
      let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

      // Increment count for the current path
      pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

      // Store updated data back to localStorage
      localStorage.setItem("page_data", JSON.stringify(pageData));
    });

    // Function to send page view data
    function sendPageData() {
      const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

      if (Object.keys(pageData).length > 0) {
        fetch(location.origin + "/librarian/track-page-view/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": "{{ csrf_token }}",
          },
          body: JSON.stringify(pageData),
        })

          .then(() => {
            localStorage.removeItem("page_data");
          })
          .catch((error) => console.error("Error sending page data:", error));
          localStorage.removeItem("page_data");
      } else {

        console.log("No page data to send");
      }
    }

    // Send data every 10 seconds
    setInterval(sendPageData, 10000);
</script>
</html>