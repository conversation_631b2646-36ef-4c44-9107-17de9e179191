body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    background-color: #f8f9fa;font-family: 'Comfortaa', sans-serif;
}

.main-container { max-width: 1000px; margin: 0.5rem auto;  }
.header-section { background: linear-gradient(135deg, #2c3e50, #3498db); color: white; padding: 2rem; border-radius: 10px; margin: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
.form-card, .seat-container { background: white; border: none; border-radius: 10px; margin: 0.3rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 2rem; }
.seat-wrapper { position: relative; }
.seats_flex{
    display: flex; 
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.seat-container {
    padding-bottom: 0.5rem;
}

.form-card {
    margin: 0.5rem;
    padding: 1rem;
}

.seat-box { 
    padding: 1rem;
    margin: 0.5rem;
    width: 100px; 
    height: 90px; 
    border-radius: 8px; 
    display: flex; 
    display: inline;
    text-align: center;
    line-height: 50px;
    align-items: center; 
    justify-content: center; 
    font-weight: 600; 
    cursor: pointer; 
    transition: all 0.3s ease;
}
.seat {
    width: 100px;
    height: 100px;
    margin: 5px;
    border-radius: 5px;
    display: inline;
    text-align: center;
    line-height: 50px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
.seat-box.available { background-color: #198754; color: white; }
.seat-box.occupied { background-color: #dc3545; color: white; }
.seat-box:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); }
.seat-controls {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
    background: white;
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
}
.seat-controls.active {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    gap: 8px;
}
.seat-controls button {
    border: none;
    background: none;
    padding: 2px 4px;
    cursor: pointer;
    transition: color 0.2s ease;
}
.seat-controls button:hover {
    color: #0d6efd;
}
.seat-controls:before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid white;
}
.legend { display: flex; flex-wrap: wrap; justify-content: center; gap: 1rem; margin: 1rem 0; }
.legend-item { display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; cursor: pointer; }
.legend-box { width: 20px; height: 20px; border-radius: 4px; }
.legend-box.available { background-color: #198754; }
.legend-box.occupied { background-color: #dc3545; }
.curr{
    border: 2px solid green;
    border-radius: 10px;
}
.shift-btn-cont{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    /* gap: 5px; */
}
.shift-btn{
    padding: 10px;
    border: 2px solid black;
    background-color: white;
    color: black;
    border-radius: 5px;
    margin: 10px;
    font-weight: 600;
}


.main-content {
    margin-left: 250px;
    padding: 4px;
    transition: all 0.3s;
}


hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}





.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

.modal-body p {
    font-size: 0.9rem;
}

hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.form-control {
    /* border: none; */
    outline: none;
    box-shadow: none;
}

.form-control:focus {
    /* border: none; */
    outline: none;
    box-shadow: none;
}

.search-wrapper {
    position: relative;
}

.search-wrapper input[type="search"] {
    padding-left: 2.5rem;
}

.search-wrapper .fa-magnifying-glass {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0b0b0b;
}

.card {
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card .card-body {
    position: relative;
    padding: 20px;
}

.card .card-body .icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.15);
}

.card .card-footer {
    background-color: transparent;
    border-top: none;
}

  

          /* Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    background-color: #f1f8ff;
    padding: 10px 0;
}

.footer-menu a,
.footer-menu .settings-link {
    color: #000000;
    font-size: 24px;
    text-align: center;
    text-decoration: none;
    position: relative;
}

.footer-menu a.active i,
.footer-menu .settings-link.active i {
    color: #020202;
}

.footer-menu .settings-link .submenu {
    display: none;
    position: absolute;
    bottom: 65px;
    left: -250px;
    background-color: #ffffff;
    border: 1px solid #000000;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
    padding: 10px;
    white-space: nowrap;
}

.footer-menu .settings-link a {
    display: block;
    color: #000000;
    padding: 5px 10px;
    text-decoration: none;
    text-align: start;
    margin-top: 10px;
}

.footer-menu .settings-link a:hover {
    background-color: #000000;
    color: white;
    border-radius: 3px;
}

.footer-menu .settings-link.active {
    display: block;
}

.submenu {
    display: none;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 0 10px rgb(192, 221, 253);
    left: 0;
    margin-top: 5px;
    padding: 10px;
    height: auto;
    width: 310px;
}

.sub-submenu {
    display: none;
    position: absolute;
    left: 100%;
    /* Position sub-submenu to the right of submenu */
    top: 0;
    margin-top: -10px;
    /* Adjust top margin as needed */
    padding: 5px 0;
    /* Add padding to sub-submenu */
    background-color: #f9f9f9;
    /* Adjust background color */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
    /* Optional: Add shadow for better visibility */
}

.settings-link:hover .submenu,
.settings-link:focus .submenu {
    display: block;
}

.sub-submenu {
    display: none;
}

.submenu-item:hover .sub-submenu,
.submenu-item:focus .sub-submenu {
    display: block;
}

.btn-success {
    background-color: white;
    color: #000000;
}

.btn-success:hover {
    background-color: green;
    color: white;
}

.btn-danger {
    background-color: white;
    color: rgb(0, 0, 0);
}

.btn-danger:hover {
    background-color: rgb(230, 17, 17);
    color: rgb(255, 255, 255);
}

.btn-link {
    color: #000000;
    padding-left: 2px;
    margin-bottom: -12px;
}

.btn-link:hover {
    color: #000000;
    text-decoration: none;
}


#enveloppe {
    color: #5bc0de;
    background-color: white;
    border: none;
}

#enveloppe:hover {
    background-color: #5bc0de;
    color: white;
    border: none;
}


.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;
    padding-bottom: 20px;

}

/* 
.footer p {
    font-size: 12px;
    padding-top: 5px;
} */

@media (max-width:768px) {
    .footer {
        margin-bottom: 50px;
    }

    .footer img {
        width: 60%;
        padding-bottom: 60px;
    }

}
#notificationBtn{
display: none;
}

#email-popup {
position: fixed;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
background: white;
padding: 20px;
border: 1px solid #ccc;
box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}
.popup-content {
text-align: center;
}

.center-text {
    text-align: center;
}

.avatar-img {
    width: 40px;
    height: 40px;
}
.icon-text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-text-wrapper i {
    font-size: 24px;
    /* Adjust the icon size as needed */
    margin-bottom: 5px;
    /* Space between icon and text */
}

.dashboard-text {
    font-size: 12px;
    /* Match the font size to the icon size */
    line-height: 24px;
    /* Set the line-height to match the icon's height */
}

@media only screen and (max-width: 767px) {
    .small_p p{
    font-size: 12px !important;
}
}
