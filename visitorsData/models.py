from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from librarian.models import *
from subLibrarian.models import Sublibrarian_param
from studentsData.models import *


class Visitor(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    sublibrarian = models.ForeignKey(
        Sublibrarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    date = models.DateField(default=timezone.now, blank=True, null=True)
    name = models.CharField(max_length=100)
    inqid = models.CharField(max_length=50, unique=True, blank=True)
    contact = models.BigIntegerField()
    email = models.EmailField(max_length=100)
    shift = models.ManyToManyField(Shift)
    notes = models.TextField()
    callback = models.DateField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        default="pending",
    )
    slug = models.SlugField(max_length=250, unique=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.inqid})"

    def generate_inqid(self):
        librarian_name = self.librarian.user.first_name.replace(" ", "_").lower()
        today = timezone.now()
        date_str = today.strftime("%m%y")

        # Get the count of Visitor entries for the current day and year
        count = (
            Visitor.objects.filter(
                inqid__startswith=f"inq_{librarian_name}_{date_str}"
            ).count()
            + 1
        )

        # Ensure the count does not exceed 999
        if count > 999:
            raise ValueError(
                "The maximum number of inquiries for this day has been reached."
            )

        # Format the inqid with the prefix, date, and count
        return f"inq_{librarian_name}_{date_str}_{count:03d}"

    def save(self, *args, **kwargs):
        if not self.inqid:
            self.inqid = self.generate_inqid()

        # Generate slug from name if not provided
        if not self.slug:
            self.slug = slugify(f"{self.name}-{self.sublibrarian}")

        super().save(*args, **kwargs)
