body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Helvetica Neue', sans-serif;
    background: #cee3e0;
    color: #333;
}


.section-title h2 {
    font-size: 36px;
    color: #333;
    text-align: center;
    font-weight: 700;
}

.section-title p {
    font-size: 16px;
    color: #666;
    text-align: center;
    margin-bottom: 50px;
}

/* Pricing Section */
.pricing-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding-bottom: 20px;
}

.pricing .col-md-4 {
    flex: 0 0 300px;
    margin-right: 20px;
    margin-bottom: 10px;
}



.pricing .box {
    height: 100%;
    padding: 30px 20px;
    border-radius: 1rem !important;
    background: #f0f0f0;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.box h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #222;
    font-weight: 600;
}

.box h4 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
    font-weight: 700;
}

.box ul {
    padding: 0;
    list-style: none;
    margin: 0 0 0 0;
    color: #666;
}

.box ul li {
    font-size: 16px;
    margin-bottom: 15px;
    position: relative;
    padding-left: 1.5rem;
    text-align: left;
}

.box ul li:before {
    content: '\f00c';
    font-family: FontAwesome;
    position: absolute;
    left: 0;
    color: #28a745;
}

.box ul li.na {
    color: #ccc;
    text-decoration: line-through;
}

.box ul li.na:before {
    content: '\f00d';
    color: #ccc;
}

.box hr {
    border-top: 1px solid #ddd;
}

.btn-wrap {
    /* margin-top: 20px; */
}

.btn-buy {
    background: #28a745;
    color: #fff;
    padding: 10px 20px;
    border-radius: 50px;
    transition: background 0.3s;
    display: inline-block;
    font-weight: 600;
    text-decoration: none;
}

.btn-buy:hover {
    background: #4a28a7 !important;
    color: #fff !important;
    padding: 10px 20px;
    border-radius: 50px;
    transition: background 0.3s;
    display: inline-block;
    font-weight: 600;
    text-decoration: none;
}

/* Ribbon Style */
.ribbon {
    width: 80px;
    height: 80px;
    position: absolute;
    top: -5px;
    left: -5px;
    overflow: hidden;
}

.ribbon::before,
.ribbon::after {
    position: absolute;
    z-index: -1;
    content: '';
    display: block;
    border: 5px solid #ff4c4c;
}

.ribbon::before {
    top: 0;
    right: 0;
    border-top-color: transparent;
    border-right-color: transparent;
}

.ribbon::after {
    bottom: 0;
    left: 0;
    border-bottom-color: transparent;
    border-left-color: transparent;
}

.ribbon div {
    position: absolute;
    width: 125px;
    top: 30px;
    left: -20px;
    padding: 5px 0;
    background: #ff4c4c;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    color: #fff;
    text-align: center;
    transform: rotate(-45deg);
    font-weight: 700;
    font-size: 0.6rem;
    text-transform: uppercase;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .pricing .col-md-4 {
        flex: 0 0 300px;
    }
}

@media (max-width: 768px) {
    .pricing .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-right: 0;
    }

    .box {
        margin: 0.5rem;
    }
}

.recommended-badge {
    display: inline-block;
    padding: 5px 10px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background-color: #FF9800;
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}
