<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTP Verification</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    


    <!-- Custom styles -->
  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
        
    
        <style>
    
            body {
                -webkit-user-select: none; /* Disable text selection */
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                margin: 0;
                background: linear-gradient(135deg, #71b7e6, #9b59b6);
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            
       
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .card-body {
            padding: 2.5rem;
            position: relative;
        }
        .btn-custom {
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }
        .btn-custom:hover {
            background-color: #0056b3;
        }
        .form-control {
            border-radius: 50px;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            box-shadow: none;
            border-color: #007bff;
        }
        .alert-dismissible .close {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0.75rem 1.25rem;
            color: inherit;
        }
        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 1rem;
        }
        .otp-icon {
            font-size: 3rem;
            color: #007bff;
            text-align: center;
            margin-bottom: 1rem;
        }
        .otp-description {
            text-align: center;
            margin-bottom: 1rem;
            color: #555;
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    <div class="otp-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="card-title">OTP Verification</h3>
                    <p class="otp-description">Please enter the OTP sent to your email to verify your account.</p>
                    <form method="POST">
                        {% csrf_token %}
                        <div class="form-group d-flex align-items-center">
                            <input type="text" class="form-control" id="otp" name="otp" placeholder="Enter your OTP" required style="height: 3rem">
                           <button id="resendOtp" class="btn btn-custom ml-3 w-100" disabled hidden>Resend OTP</button>
                        </div>
                        <button type="submit" class="btn btn-custom btn-block mt-3">Verify OTP</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const resendButton = document.getElementById('resendOtp');

                // Show the button after 3 minutes
                setTimeout(() => {
                    resendButton.disabled = false;
                    resendButton.hidden = false;
                }, 1000); // 3 minutes in milliseconds (3 * 60 * 1000)

                // Redirect to /students/resend_otp/ page when the "Resend OTP" button is clicked
                resendButton.addEventListener('click', function (event) {
                    event.preventDefault();
                    window.location.href = '/librarian/resend-otp/';
                });
            });
        </script>
    
    <!-- Bootstrap JS and dependencies -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
