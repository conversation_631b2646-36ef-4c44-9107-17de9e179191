<!DOCTYPE html>
<html>
<head>
    <title>Pay for Advertisement</title>
    <meta name="google" content="notranslate">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
              body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>
</head>
<body>

    <h1>Pay for Advertisement</h1>
    <p>Advertisement: {{ ad.subject }}</p>
    <p>Amount: {{ ad.amount }}</p>

    <button id="rzp-button1">Pay Now</button>

    <script>
        var options = {
            "key": "{{ settings.RAZORPAY_KEY_ID }}",
            "amount": "{{ request.session.razorpay_amount }}00",  // amount in the smallest currency unit
            "currency": "INR",
            "name": "{{ ad.subject }}",
            "description": "Payment for advertisement",
            "image": "https://example.com/your_logo.png",
            "order_id": "{{ request.session.razorpay_order_id }}",
            "handler": function (response) {
                // Handle the payment success response
                // You can redirect the user or perform any other actions
                alert('Payment successful!');
                window.location.href = "{% url 'advertisement_detail' ad.pk %}";
            },
            "prefill": {
                "name": "Gaurav Kumar",
                "email": "<EMAIL>",
                "contact": "9999999999"
            },
            "notes": {
                "address": "Razorpay Corporate Office"
            },
            "theme": {
                "color": "#3399cc"
            }
        };
        var rzp1 = new Razorpay(options);
        document.getElementById('rzp-button1').onclick = function (e) {
            rzp1.open();
            e.preventDefault();
        }
    </script>
</body>
</html>