/**
* Template Name: Knight - v4.3.0
* Template URL: https://bootstrapmade.com/knight-free-bootstrap-theme/
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/
body {
  font-family: "Open Sans", sans-serif;
}

a {
  text-decoration: none;
  color: #0A696D;
}

a:hover {
  color: #47a1a4;
  text-decoration: none;
}



/*--------------------------------------------------------------
# Back to top button
--------------------------------------------------------------*/
.back-to-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: 15px;
  z-index: 996;
  background: #0A696D;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.4s;
}

.back-to-top i {
  font-size: 28px;
  color: #fff;
  line-height: 0;
}

.back-to-top:hover {
  background: #47a1a4;
  color: #fff;
}

.back-to-top.active {
  visibility: visible;
  opacity: 1;
}

/*--------------------------------------------------------------
# Disable AOS delay on mobile
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#header {
  height: 90px;
  transition: all 0.5s;
  z-index: 997;
  transition: all 0.5s;
  background: #fff;
  box-shadow: 0 4px 10px -3px rgba(191, 191, 191, 0.5);
}

#header .logo h1 {
  font-size: 28px;
  margin: 0;
  line-height: 1;
  font-weight: 400;
  letter-spacing: 3px;
  text-transform: uppercase;
}

#header .logo h1 a, #header .logo h1 a:hover {
  color: #fff;
  text-decoration: none;
}

#header .logo img {
  padding: 0;
  margin: 0;
  max-height: 60px;
}

@media (max-width: 992px) {
  #header {
    height: 70px;
  }
}

.scrolled-offset {
  margin-top: 90px;
}

@media (max-width: 992px) {
  .scrolled-offset {
    margin-top: 90px;
  }
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/**
* Desktop Navigation 
*/
.navbar {
  padding: 0;
}

.navbar ul {
  margin: 0;
  padding: 0;
  display: flex;
  list-style: none;
  align-items: center;
}

.navbar li {
  position: relative;
}

.navbar a, .navbar a:focus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0 10px 30px;
  font-size: 15px;
  color: #151515;
  text-transform: uppercase;
  white-space: nowrap;
  transition: 0.3s;
}

.navbar a i, .navbar a:focus i {
  font-size: 12px;
  line-height: 0;
  margin-left: 5px;
}

.navbar a:hover, .navbar .active, .navbar .active:focus, .navbar li:hover > a {
  color: #47a1a4;
}

.navbar .dropdown ul {
  display: block;
  position: absolute;
  left: 14px;
  top: calc(100% + 30px);
  margin: 0;
  padding: 10px 0;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  background: #fff;
  box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
  transition: 0.3s;
  /* bottom: 0; */
}

.navbar .dropdown ul li {
  min-width: 200px;
}

.navbar .dropdown ul a {
  padding: 10px 20px;
  font-size: 15px;
  text-transform: none;
  font-weight: 600;
}

.navbar .dropdown ul a i {
  font-size: 12px;
}

.navbar .dropdown ul a:hover, .navbar .dropdown ul .active:hover, .navbar .dropdown ul li:hover > a {
  color: #47a1a4;
}

.navbar .dropdown:hover > ul {
  opacity: 1;
  top: 100%;
  visibility: visible;
}

.navbar .dropdown .dropdown ul {
  top: 0;
  left: calc(100% - 30px);
  visibility: hidden;
}

.navbar .dropdown .dropdown:hover > ul {
  opacity: 1;
  top: 0;
  left: 100%;
  visibility: visible;
}

@media (max-width: 1366px) {
  .navbar .dropdown .dropdown ul {
    left: -90%;
  }
  .navbar .dropdown .dropdown:hover > ul {
    left: -100%;
  }
}

/**
* Mobile Navigation 
*/
.mobile-nav-toggle {
  color: #151515;
  font-size: 28px;
  cursor: pointer;
  display: none;
  line-height: 0;
  transition: 0.5s;
}

.mobile-nav-toggle.bi-x {
  color: #fff;
}

@media (max-width: 991px) {
  .mobile-nav-toggle {
    display: block;
  }
  .navbar ul {
    display: none;
  }
}

.navbar-mobile {
  position: fixed;
  overflow: hidden;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  transition: 0.3s;
  z-index: 999;
}

.navbar-mobile .mobile-nav-toggle {
  position: absolute;
  top: 15px;
  right: 15px;
}

.navbar-mobile ul {
  display: block;
  position: absolute;
  top: 55px;
  right: 15px;
  bottom: 15px;
  left: 15px;
  padding: 10px 0;
  background-color: #fff;
  overflow-y: auto;
  transition: 0.3s;
}

.navbar-mobile a {
  padding: 10px 20px;
  font-size: 15px;
  color: #151515;
}

.navbar-mobile a:hover, .navbar-mobile .active, .navbar-mobile li:hover > a {
  color: #47a1a4;
}

.navbar-mobile .getstarted {
  margin: 15px;
}

.navbar-mobile .dropdown ul {
  position: static;
  display: none;
  margin: 10px 20px;
  padding: 10px 0;
  z-index: 99;
  opacity: 1;
  visibility: visible;
  background: #fff;
  box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
}

.navbar-mobile .dropdown ul li {
  min-width: 200px;
}

.navbar-mobile .dropdown ul a {
  padding: 10px 20px;
}

.navbar-mobile .dropdown ul a i {
  font-size: 12px;
}

.navbar-mobile .dropdown ul a:hover, .navbar-mobile .dropdown ul .active:hover, .navbar-mobile .dropdown ul li:hover > a {
  color: #47a1a4;
}

.navbar-mobile .dropdown > .dropdown-active {
  display: block;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
#hero {
  width: 100%;
  /* height: 100vh; */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.dev/svgjs' width='1400' height='720' preserveAspectRatio='none' viewBox='0 0 1400 720'%3e%3cg mask='url(%26quot%3b%23SvgjsMask3709%26quot%3b)' fill='none'%3e%3crect width='1400' height='720' x='0' y='0' fill='url(%26quot%3b%23SvgjsRadialGradient3710%26quot%3b)'%3e%3c/rect%3e%3cg mask='url(%26quot%3b%23SvgjsMask3711%26quot%3b)'%3e%3cpath d='M228 420L252 420L276 420L300 420M396 396L420 372L444 372L468 372L492 372L516 372L540 372L564 372L588 372M276 396L300 372L324 372L348 372L372 372M204 420L228 396L252 372L276 372L300 348L324 324L348 300L372 300L396 324L420 348M84 372L108 348L132 348M-12 372L12 372L36 372L60 372L84 372L108 372L132 396L156 420L180 420L204 420L228 420L252 396L276 396L300 396L324 396L348 396L372 396L396 396L420 396L444 396L468 396L492 396' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M486 396 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM294 420 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM582 372 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM366 372 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM414 348 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM126 348 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M420 324L444 300M300 300L324 276M396 300L420 300M228 300L252 276M252 300L276 324L300 324M-12 348L12 348L36 348L60 348L84 324L108 324L132 324L156 324L180 324L204 300L228 300L252 300L276 300L300 300L324 300L348 276L372 276L396 300L420 324L444 324L468 324' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M462 324 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM438 300 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM318 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM414 300 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM246 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM294 324 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M-12 396L12 396L36 396L60 396L84 396' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M78 396 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M396 252L420 276L444 276L468 276M276 252L300 276M84 300L108 276L132 276M348 252L372 228L396 228L420 228L444 228L468 228L492 228M204 276L228 276M-12 324L12 324L36 324L60 324L84 300L108 300L132 300L156 300L180 300L204 276L228 252L252 252L276 252L300 252L324 252L348 252L372 252L396 252L420 252L444 252L468 252L492 252' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M486 252 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM462 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM294 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM126 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM486 228 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM222 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M420 468L444 444M252 444L276 444L300 444L324 444M444 468L468 492M372 468L396 492L420 492L444 492M468 468L492 444L516 444L540 444M-12 420L12 420L36 420L60 420L84 420L108 420L132 420L156 444L180 444L204 444L228 444L252 444L276 468L300 468L324 468L348 468L372 468L396 468L420 468L444 468L468 468L492 468' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M486 468 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM438 444 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM318 444 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM462 492 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM438 492 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM534 444 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M60 252L84 276M36 276L60 276M12 300L36 300M-12 300L12 300L36 276L60 252L84 252L108 252L132 252' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M126 252 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM78 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM54 276 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM30 300 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M-12 444L12 444L36 444L60 444L84 444L108 444L132 444L156 468L180 468L204 468L228 468L252 468' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M246 468 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M180 252L204 228L228 204M132 228L156 204L180 204M156 228L180 228M-12 276L12 276L36 252L60 228L84 228L108 228L132 228L156 228L180 252L204 252L228 228L252 228L276 228L300 228' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M294 228 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM222 204 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM174 204 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM174 228 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M492 516L516 492L540 492L564 492M636 516L660 492M516 516L540 540M420 516L444 540L468 540L492 540L516 540L540 564L564 564L588 564L612 564L636 564M300 492L324 516M324 492L348 492L372 492M348 516L372 540M-12 468L12 468L36 468L60 468L84 468L108 468L132 468L156 492L180 492L204 492L228 492L252 492L276 492L300 492L324 492L348 516L372 516L396 516L420 516L444 516L468 516L492 516L516 516L540 516L564 516L588 516L612 516L636 516L660 516' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M654 516 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM558 492 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM654 492 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM534 540 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM630 564 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM318 516 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM366 492 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM366 540 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M420 204L444 180L468 180L492 180L516 180M372 204L396 180M516 204L540 228L564 228M108 180L132 204M60 204L84 204L108 204M348 204L372 180L396 156L420 156M-12 252L12 252L36 228L60 204L84 180L108 180L132 180L156 180L180 180L204 180L228 180L252 180L276 180L300 204L324 204L348 204L372 204L396 204L420 204L444 204L468 204L492 204L516 204L540 204' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M534 204 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM510 180 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM390 180 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM558 228 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM126 204 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM102 204 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM414 156 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M-12 492L12 492L36 492L60 492L84 492L108 492L132 492L156 516L180 516L204 516L228 516' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M222 516 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M-12 228L12 228L36 204L60 180L84 156L108 156L132 156L156 156L180 156L204 156L228 156L252 156L276 156L300 156' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M294 156 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3cpath d='M468 564L492 588L516 612M636 588L660 612L684 612L708 612L732 612L756 612M324 588L348 612M252 588L276 612L300 612L324 612L348 636L372 636M276 588L300 564M36 516L60 540L84 540L108 540L132 540L156 564M684 588L708 564M612 588L636 612L660 636L684 636M-12 516L12 516L36 516L60 516L84 516L108 516L132 516L156 540L180 564L204 564L228 588L252 588L276 588L300 588L324 588L348 588L372 588L396 564L420 564L444 564L468 564L492 564L516 588L540 588L564 588L588 588L612 588L636 588L660 588L684 588L708 588L732 588' stroke='rgba(255%2c 255%2c 255%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M726 588 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM510 612 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM750 612 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM342 612 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM366 636 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM294 564 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM150 564 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM702 564 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM678 636 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M180 132L204 108L228 108M276 132L300 108M348 132L372 156M252 132L276 108L300 84L324 84L348 108M-12 204L12 204L36 180L60 156L84 132L108 132L132 132L156 132L180 132L204 132L228 132L252 132L276 132L300 132L324 132L348 132L372 132L396 108' stroke='rgba(0%2c 0%2c 0%2c 1)' stroke-width='4'%3e%3c/path%3e%3cpath d='M390 108 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM222 108 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM294 108 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM366 156 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0zM342 108 a6 6 0 1 0 12 0 a6 6 0 1 0 -12 0z' fill='rgba(0%2c 0%2c 0%2c 1)'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask3709'%3e%3crect width='1400' height='720' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cradialGradient cx='50%25' cy='50%25' r='787.15' gradientUnits='userSpaceOnUse' id='SvgjsRadialGradient3710'%3e%3cstop stop-color='%230e2a47' offset='0'%3e%3c/stop%3e%3cstop stop-color='rgba(10%2c 105%2c 109%2c 0.93)' offset='1'%3e%3c/stop%3e%3c/radialGradient%3e%3cmask id='SvgjsMask3711'%3e%3crect width='1400' height='720' fill='white'%3e%3c/rect%3e%3cpath d='M489 396 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM297 420 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM585 372 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM369 372 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM417 348 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM129 348 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M465 324 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM441 300 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM321 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM417 300 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM249 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM297 324 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M81 396 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M489 252 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM465 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM297 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM129 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM489 228 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM225 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M489 468 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM441 444 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM321 444 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM465 492 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM441 492 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM537 444 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M129 252 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM81 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM57 276 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM33 300 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M249 468 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M297 228 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM225 204 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM177 204 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM177 228 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M657 516 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM561 492 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM657 492 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM537 540 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM633 564 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM321 516 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM369 492 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM369 540 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M537 204 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM513 180 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM393 180 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM561 228 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM129 204 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM105 204 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM417 156 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M225 516 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M297 156 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M729 588 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM513 612 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM753 612 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM345 612 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM369 636 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM297 564 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM153 564 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM705 564 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM681 636 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3cpath d='M393 108 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM225 108 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM297 108 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM369 156 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0zM345 108 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' fill='black'%3e%3c/path%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

#hero .hero-container {
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  padding: 0 15px;
}

#hero .hero-logo {
  margin-bottom: 30px;
}

#hero h1 {
  margin: 0 0 30px 0;
  font-size: 48px;
  font-weight: 500;
  line-height: 56px;
  color: #fff;
}

#hero h2 {
  color: #aeaeae;
  margin-bottom: 30px;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
}

#hero .btn-get-started {
  font-family: "Montserrat", sans-serif;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 16px;
  letter-spacing: 1px;
  display: inline-block;
  padding: 14px 30px;
  border-radius: 3px;
  margin: 10px;
  color: #fff;
  background: #0A696D;
}

#hero .btn-get-started:hover {
  transition: 0.5s;
  background-color: #47a1a4;
  color: white;
}

@media (min-width: 1024px) {
  #hero {
    background-attachment: fixed;
  }
}

@media (max-width: 768px) {
  #hero h1 {
    font-size: 28px;
    line-height: 36px;
  }
  #hero h2 {
    line-height: 22px;
  }
}

/*--------------------------------------------------------------
# Sections General
--------------------------------------------------------------*/
section {
  padding: 60px 0;
  overflow: hidden;
}

.section-bg {
  background-color: whitesmoke;
}

.section-title {
  text-align: center;
  padding-bottom: 40px;
}

.section-title h2 {
  font-size: 32px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 15px;
  padding-bottom: 0;
  color: #151515;
}

.section-title p {
  margin-bottom: 0;
  color: #aeaeae;
}

/*--------------------------------------------------------------
# About Us
--------------------------------------------------------------*/
.about .image {
  padding: 20px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
}

.about .content h3 {
  font-weight: 600;
  font-size: 26px;
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  padding-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.about .content ul i {
  font-size: 24px;
  padding: 2px 6px 0 0;
  color: #0A696D;
}

.about .content p:last-child {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.services .icon-box h4 {
  font-size: 20px;
  font-weight: 700;
  margin: 5px 0 10px 60px;
}

.services .icon-box i {
  font-size: 48px;
  float: left;
  color: #0A696D;
}

.services .icon-box p {
  font-size: 15px;
  color: #959595;
  margin-left: 60px;
}

.services .image {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 400px;
}

/*--------------------------------------------------------------
# Featured
--------------------------------------------------------------*/
.featured {
  padding: 40px 0 0 0;
  background: white;
}

.featured .nav-tabs {
  border: 0;
}

.featured .nav-link {
  border: 0;
  padding: 20px;
  color: #151515;
  transition: 0.3s;
}

.featured .nav-link h4 {
  font-size: 18px;
  font-weight: 600;
  transition: 0.3s;
}

.featured .nav-link:hover h4 {
  color: #0A696D;
}

.featured .nav-link p {
  font-size: 14px;
  margin-bottom: 0;
}

.featured .nav-link.active {
  box-shadow: 0px 0 25px rgba(0, 0, 0, 0.08);
}

.featured .nav-link.active h4 {
  color: #0A696D;
}

.featured .tab-pane.active {
  -webkit-animation: slide-down 0.5s ease-out;
  animation: slide-down 0.5s ease-out;
}

@-webkit-keyframes slide-down {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slide-down {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/*--------------------------------------------------------------
# Why Us
--------------------------------------------------------------*/
.why-us {
  background: whitesmoke;
  padding: 0;
}

.why-us .content {
  padding: 60px 100px 0 100px;
}

.why-us .content h3 {
  font-weight: 400;
  font-size: 34px;
}

.why-us .content h4 {
  font-size: 20px;
  font-weight: 700;
  margin-top: 5px;
}

.why-us .content p {
  font-size: 15px;
  color: #959595;
}

.why-us .video-box {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  min-height: 400px;
  position: relative;
}

.why-us .accordion-list {
  padding: 0 100px 60px 100px;
}

.why-us .accordion-list ul {
  padding: 0;
  list-style: none;
}

.why-us .accordion-list li+li {
  margin-top: 15px;
}

.why-us .accordion-list li {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.why-us .accordion-list a {
  display: block;
  position: relative;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  padding-right: 30px;
  outline: none;
  cursor: pointer;
}

.why-us .accordion-list span {
  color: #0A696D;
  font-weight: 600;
  font-size: 18px;
  padding-right: 10px;
}

.why-us .accordion-list i {
  font-size: 24px;
  position: absolute;
  right: 0;
  top: 0;
}

.why-us .accordion-list p {
  margin-bottom: 0;
  padding: 10px 0 0 0;
}

.why-us .accordion-list .icon-show {
  display: none;
}

.why-us .accordion-list a.collapsed {
  color: #343a40;
}

.why-us .accordion-list a.collapsed:hover {
  color: #0A696D;
}

.why-us .accordion-list a.collapsed .icon-show {
  display: inline-block;
}

.why-us .accordion-list a.collapsed .icon-close {
  display: none;
}

.why-us .play-btn {
  width: 94px;
  height: 94px;
  background: radial-gradient(#0A696D 50%, rgba(124, 197, 118, 0.4) 52%);
  border-radius: 50%;
  display: block;
  position: absolute;
  left: calc(50% - 47px);
  top: calc(50% - 47px);
  overflow: hidden;
}

.why-us .play-btn::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 100;
  transition: all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.why-us .play-btn::before {
  content: '';
  position: absolute;
  width: 120px;
  height: 120px;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation: pulsate-btn 2s;
  animation: pulsate-btn 2s;
  -webkit-animation-direction: forwards;
  animation-direction: forwards;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: steps;
  animation-timing-function: steps;
  opacity: 1;
  border-radius: 50%;
  border: 5px solid rgba(124, 197, 118, 0.7);
  top: -15%;
  left: -15%;
  background: rgba(198, 16, 0, 0);
}

.why-us .play-btn:hover::after {
  border-left: 15px solid #0A696D;
  transform: scale(20);
}

.why-us .play-btn:hover::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border: none;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 200;
  -webkit-animation: none;
  animation: none;
  border-radius: 0;
}

@media (max-width: 1024px) {

  .why-us .content,
  .why-us .accordion-list {
    padding-left: 0;
    padding-right: 0;
  }
}

@media (max-width: 992px) {
  .why-us .content {
    padding-top: 30px;
  }

  .why-us .accordion-list {
    padding-bottom: 30px;
  }
}

@-webkit-keyframes pulsate-btn {
  0% {
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}

@keyframes pulsate-btn {
  0% {
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# Portfolio
--------------------------------------------------------------*/
.portfolio .portfolio-item {
  margin-bottom: 30px;
}

.portfolio #portfolio-flters {
  padding: 0;
  margin: 0 auto 25px auto;
  list-style: none;
  text-align: center;
}

.portfolio #portfolio-flters li {
  cursor: pointer;
  display: inline-block;
  padding: 10px 18px 12px 18px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  text-transform: uppercase;
  color: #555555;
  transition: all 0.3s ease-in-out;
  margin: 0 4px 10px 4px;
  background: whitesmoke;
  border-radius: 4px;
}

.portfolio #portfolio-flters li:hover,
.portfolio #portfolio-flters li.filter-active {
  background: #0A696D;
  color: #fff;
}

.portfolio #portfolio-flters li:last-child {
  margin-right: 0;
}

.portfolio .portfolio-wrap {
  transition: 0.3s;
  position: relative;
  overflow: hidden;
  z-index: 1;
  background: rgba(0, 0, 0, 0.6);
}

.portfolio .portfolio-wrap::before {
  content: "";
  background: rgba(21, 21, 21, 0.6);
  position: absolute;
  left: 30px;
  right: 30px;
  top: 30px;
  bottom: 30px;
  transition: all ease-in-out 0.3s;
  z-index: 2;
  opacity: 0;
}

.portfolio .portfolio-wrap .portfolio-info {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  z-index: 3;
  transition: all ease-in-out 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.portfolio .portfolio-wrap .portfolio-info::before {
  display: block;
  content: "";
  width: 48px;
  height: 48px;
  position: absolute;
  top: 35px;
  left: 35px;
  border-top: 3px solid #fff;
  border-left: 3px solid #fff;
  transition: all 0.5s ease 0s;
  z-index: 9994;
}

.portfolio .portfolio-wrap .portfolio-info::after {
  display: block;
  content: "";
  width: 48px;
  height: 48px;
  position: absolute;
  bottom: 35px;
  right: 35px;
  border-bottom: 3px solid #fff;
  border-right: 3px solid #fff;
  transition: all 0.5s ease 0s;
  z-index: 9994;
}

.portfolio .portfolio-wrap .portfolio-info h4 {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}

.portfolio .portfolio-wrap .portfolio-info p {
  color: #ffffff;
  font-size: 14px;
  text-transform: uppercase;
  padding: 0;
  margin: 0;
}

.portfolio .portfolio-wrap .portfolio-links {
  text-align: center;
  z-index: 4;
}

.portfolio .portfolio-wrap .portfolio-links a {
  color: #fff;
  margin: 0 2px;
  font-size: 28px;
  display: inline-block;
  transition: 0.3s;
}

.portfolio .portfolio-wrap .portfolio-links a:hover {
  color: #7cc576;
}

.portfolio .portfolio-wrap:hover::before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 1;
}

.portfolio .portfolio-wrap:hover .portfolio-info {
  opacity: 1;
}

.portfolio .portfolio-wrap:hover .portfolio-info::before {
  top: 15px;
  left: 15px;
}

.portfolio .portfolio-wrap:hover .portfolio-info::after {
  bottom: 15px;
  right: 15px;
}

/*--------------------------------------------------------------
# Portfolio Details
--------------------------------------------------------------*/
.portfolio-details {
  padding-top: 40px;
}

.portfolio-details .portfolio-details-slider img {
  width: 100%;
}

.portfolio-details .portfolio-details-slider .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: #fff;
  opacity: 1;
  border: 1px solid #0A696D;
}

.portfolio-details .portfolio-details-slider .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #0A696D;
}

.portfolio-details .portfolio-info {
  padding: 30px;
  box-shadow: 0px 0 30px rgba(21, 21, 21, 0.08);
}

.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li+li {
  margin-top: 10px;
}

.portfolio-details .portfolio-description {
  padding-top: 30px;
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
}

/*--------------------------------------------------------------
# Testimonials
--------------------------------------------------------------*/
.testimonials {
  padding: 60px 0;
  background: url("../img/testimonials-bg.jpg") no-repeat;
  background-position: center center;
  background-size: cover;
  position: relative;
}

.testimonials::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

.testimonials .quote-icon {
  text-align: center;
  margin-bottom: 30px;
}

.testimonials .quote-icon i {
  color: #0A696D;
  font-size: 24px;
  padding: 18px;
  border-radius: 50px;
  border: 2px solid #0A696D;
}

.testimonials .testimonial-item {
  text-align: center;
  color: #fff;
}

.testimonials .testimonial-item p {
  font-style: italic;
  margin: 0 auto 30px auto;
  font-size: 20px;
}

.testimonials .testimonial-item .testimonial-img {
  width: 100px;
  border-radius: 50%;
  border: 6px solid rgba(255, 255, 255, 0.15);
  margin: 0 auto;
}

.testimonials .testimonial-item h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 10px 0 5px 0;
  color: #fff;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: #ddd;
  margin: 0 0 15px 0;
}

.testimonials .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.4);
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #0A696D;
}

@media (min-width: 992px) {
  .testimonials .testimonial-item p {
    width: 80%;
  }
}

/*--------------------------------------------------------------
# Clients
--------------------------------------------------------------*/
.clients {
  background: whitesmoke;
  padding: 15px 0;
  text-align: center;
}

.clients img {
  width: 50%;
  filter: grayscale(100);
  transition: all 0.4s ease-in-out;
  display: inline-block;
  padding: 15px 0;
}

.clients img:hover {
  filter: none;
  transform: scale(1.2);
}

@media (max-width: 768px) {
  .clients img {
    width: 40%;
  }
}

@media (max-width: 575px) {
  .clients img {
    width: 50%;
  }
}

/*--------------------------------------------------------------
# Team
--------------------------------------------------------------*/
.team {
  background: #fff;
  padding: 60px 0;
}

.team .member {
  margin-bottom: 20px;
  overflow: hidden;
}

.team .member .member-img {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
}

.team .member .social {
  position: absolute;
  left: 0;
  bottom: -40px;
  right: 0;
  height: 40px;
  opacity: 0;
  transition: bottom ease-in-out 0.4s;
  background: #47a1a4e4;
  display: flex;
  align-items: center;
  justify-content: center;
}

.team .member .social a {
  transition: color 0.3s;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.team .member .social a i {
  line-height: 0;
}

.team .member .social a:hover {
  color: #fff;
}

.team .member .social i {
  font-size: 18px;
  margin: 0 2px;
}

.team .member .member-info h4 {
  font-weight: 700;
  margin: 15px 0 5px 0;
  font-size: 18px;
}

.team .member .member-info span {
  display: block;
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 400;
  margin-bottom: 15px;
  color: #0A696D;
}

.team .member .member-info p {
  font-style: italic;
  font-size: 14px;
  line-height: 26px;
  color: #888888;
}

.team .member:hover .social {
  bottom: 0;
  opacity: 1;
  transition: bottom ease-in-out 0.4s;
}

/*--------------------------------------------------------------
# Pricing
--------------------------------------------------------------*/
.pricing .box {
  padding: 20px;
  background: #fff;
  text-align: center;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  border: 1px solid #fff;

}


.pricing .box h3 {
  font-weight: 400;
  padding: 15px;
  margin-top: 15px;
  font-size: 12px;
  font-weight: 600;
  color: #151515;
}

.pricing .box h4 {
  font-size: 42px;
  color: #151515;
  font-weight: 700;
  font-family: "Open Sans", sans-serif;
  margin-bottom: 20px;
}

.pricing .box h4 sup {
  font-size: 20px;
  top: -15px;
  left: -3px;
}

.pricing .box h4 span {
  color: #bababa;
  font-size: 16px;
  font-weight: 300;
}

.pricing .box ul {
  padding: 0;
  list-style: none;
  color: #151515;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  text-align: center;
  /* display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column; */
}

.pricing .box ul li {
  padding-bottom: 10px;
  /* text-align: start; */
}

.pricing .box ul i {
  color: #0A696D;
  font-size: 18px;
  padding-right: 4px;
}

.pricing .box ul .na {
  color: #ccc;
  text-decoration: line-through;
}

.pricing .box .btn-wrap {
  text-align: center;
}

.mini_buy {
  margin-top: 5rem;
}

.pricing .box .btn-buy {
  display: inline-block;
  padding: 10px 40px 12px 40px;
  border-radius: 5px;
  border: 2px solid #0A696D;
  color: #0A696D;
  font-size: 14px;
  font-weight: 400;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  transition: 0.3s;
}


.pricing .recommended {
  border-color: #0A696D;
}

.pricing .recommended .btn-buy {
  background: #0A696D;
  color: #fff;
}

.pricing .recommended {
  background: #47a1a4;
  border-color: #47a1a4;
}

.pricing .recommended-badge {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  font-size: 12px;
  padding: 4px 25px 6px 25px;
  background: #eaf6e9;
  color: #0A696D;
  border-radius: 50px;
  text-transform: uppercase;
  font-weight: 600;
}

/*--------------------------------------------------------------
# Frequently Asked Questions
--------------------------------------------------------------*/
.faq .faq-list {
  padding: 0;
  list-style: none;
}

.faq .faq-list li {
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.faq .faq-list a {
  display: block;
  position: relative;
  font-family: #0A696D;
  font-size: 18px;
  line-height: 24px;
  font-weight: 400;
  padding-right: 25px;
  cursor: pointer;
}

.faq .faq-list i {
  font-size: 24px;
  position: absolute;
  right: 0;
  top: 0;
}

.faq .faq-list p {
  margin-bottom: 0;
  padding: 10px 0 0 0;
}

.faq .faq-list .icon-show {
  display: none;
}

.faq .faq-list a.collapsed {
  color: #343a40;
}

.faq .faq-list a.collapsed:hover {
  color: #0A696D;
}

.faq .faq-list a.collapsed .icon-show {
  display: inline-block;
}

.faq .faq-list a.collapsed .icon-close {
  display: none;
}

/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.contact .info {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
}

.contact .info i {
  font-size: 20px;
  color: #0A696D;
  float: left;
  width: 44px;
  height: 44px;
  background: #eaf6e9;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
}

.contact .info h4 {
  padding: 0 0 0 60px;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #151515;
}

.contact .info p {
  padding: 0 0 0 60px;
  margin-bottom: 0;
  font-size: 14px;
  color: #484848;
}

.contact .info .email,
.contact .info .phone {
  margin-top: 40px;
}

.contact .info .email:hover i,
.contact .info .address:hover i,
.contact .info .phone:hover i {
  background: #0A696D;
  color: #fff;
}

.contact .php-email-form {
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 5px;
}

.contact .php-email-form .form-group {
  padding-bottom: 8px;
}

.contact .php-email-form .error-message {
  display: none;
  color: #fff;
  background: #ed3c0d;
  text-align: left;
  padding: 15px;
  font-weight: 600;
}

.contact .php-email-form .error-message br+br {
  margin-top: 25px;
}

.contact .php-email-form .sent-message {
  display: none;
  color: #fff;
  background: #0A696D;
  text-align: center;
  padding: 15px;
  font-weight: 600;
}

.contact .php-email-form .loading {
  display: none;
  background: #fff;
  text-align: center;
  padding: 15px;
}

.contact .php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid #0A696D;
  border-top-color: #eee;
  -webkit-animation: animate-loading 1s linear infinite;
  animation: animate-loading 1s linear infinite;
}

.contact .php-email-form input,
.contact .php-email-form textarea {
  border-radius: 0;
  box-shadow: none;
  font-size: 14px;
}

.contact .php-email-form input {
  height: 44px;
}

.contact .php-email-form textarea {
  padding: 10px 12px;
}

.contact .php-email-form button[type="submit"] {
  background: #0A696D;
  border: 0;
  padding: 10px 24px;
  color: #fff;
  transition: 0.4s;
  border-radius: 4px;
}

.contact .php-email-form button[type="submit"]:hover {
  background: #47a1a4;
}

@-webkit-keyframes animate-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes animate-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Breadcrumbs
--------------------------------------------------------------*/
.breadcrumbs {
  padding: 40px 0;
}

.breadcrumbs h2 {
  font-size: 26px;
  font-weight: 300;
}

.breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 15px;
}

.breadcrumbs ol li+li {
  padding-left: 10px;
}

.breadcrumbs ol li+li::before {
  display: inline-block;
  padding-right: 10px;
  color: #2f2f2f;
  content: "/";
}

@media (max-width: 768px) {
  .breadcrumbs .d-flex {
    display: block !important;
  }

  .breadcrumbs ol {
    display: block;
  }

  .breadcrumbs ol li {
    display: inline-block;
  }
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
#footer {
  background: url("../img/footer-bg.jpg") center center no-repeat;
  color: #fff;
  font-size: 14px;
  position: relative;
}

#footer::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  z-index: 1;
}

#footer .footer-top {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 80px 0;
}

#footer .footer-top .footer-logo img {
  height: 80px;
}

#footer .footer-top h3 {
  font-size: 36px;
  font-weight: 700;
  color: #fff;
  position: relative;
  font-family: "Poppins", sans-serif;
  padding: 30px 0 0 0;
  margin-bottom: 0;
}

#footer .footer-top p {
  font-size: 15;
  font-style: italic;
  margin: 30px 0 0 0;
  padding: 0;
}

#footer .footer-top .footer-newsletter {
  text-align: center;
  font-size: 15px;
  margin-top: 30px;
}

#footer .footer-top .footer-newsletter form {
  background: #fff;
  padding: 6px 10px;
  position: relative;
  border-radius: 50px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
  text-align: left;
}

#footer .footer-top .footer-newsletter form input[type="email"] {
  border: 0;
  padding: 4px 8px;
  width: calc(100% - 100px);
}

#footer .footer-top .footer-newsletter form input[type="submit"] {
  position: absolute;
  top: 0;
  right: -1px;
  bottom: 0;
  border: 0;
  background: none;
  font-size: 16px;
  padding: 0 20px;
  background: #0A696D;
  color: #fff;
  transition: 0.3s;
  border-radius: 50px;
  box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
}

#footer .footer-top .footer-newsletter form input[type="submit"]:hover {
  background: #47a1a4;
}

#footer .footer-top .social-links {
  margin-top: 30px;
}

#footer .footer-top .social-links a {
  font-size: 18px;
  display: inline-block;
  background: #0A696D;
  color: #fff;
  line-height: 1;
  padding: 8px 0;
  margin-right: 4px;
  border-radius: 50%;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

#footer .footer-top .social-links a:hover {
  background: #47a1a4;
  color: #fff;
  text-decoration: none;
}

#footer .footer-bottom {
  border-top: 1px solid #222222;
  z-index: 2;
  position: relative;
  padding-top: 40px;
  padding-bottom: 40px;
}

#footer .copyright {
  text-align: center;
}

#footer .credits {
  text-align: center;
  font-size: 13px;
  padding-top: 5px;
}


/* For Chrome, Safari, and Edge */
::-webkit-scrollbar {
  width: 8px; /* Adjust the width of the scrollbar */
  height: 8px; /* Adjust the height of the horizontal scrollbar */
}

::-webkit-scrollbar-thumb {
  background-color: #9bc6bf; /* Color of the scrollbar thumb */
  border-radius: 10px; /* Rounded corners for the thumb */
}

::-webkit-scrollbar-thumb:hover {
  background-color: #9bc6bf; /* Color of the scrollbar thumb when hovered */
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* Color of the scrollbar track */
  border-radius: 10px; /* Rounded corners for the track */
}
