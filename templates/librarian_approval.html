<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Librarian Approval Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

         <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --accent-color: #e74c3c;
            --background-color: #f5f5f5;
            --text-color: #333333;
            --card-bg-color: #ffffff;
        }
        
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Poppins', sans-serif;
            color: var(--text-color);
            background-color: var(--background-color);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 50px;
            padding-bottom: 50px;
        }
        
        .page-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            padding-bottom: 15px;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        .card {
            margin-bottom: 25px;
            border: none;
            border-radius: 15px;
            transition: transform 0.3s, box-shadow 0.3s;
            overflow: hidden;
            background-color: var(--card-bg-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .card-text {
            color: var(--text-color);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
        }
        
        .icon {
            margin-right: 0.75rem;
            color: var(--primary-color);
            width: 20px;
            text-align: center;
        }
        
        .btn {
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            border-radius: 50px;
        }
        
        .btn-approve {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }
        
        .btn-approve:hover {
            background-color: #27ae60;
            border-color: #27ae60;
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
        }
        
        .btn-reject {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }
        
        .btn-reject:hover {
            background-color: #c0392b;
            border-color: #c0392b;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        
        .approval-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }
        
        .approval-actions .btn {
            margin-left: 1rem;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1 class="page-title mb-5">Librarian Approval Dashboard</h1>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-book-reader icon"></i>Library Name : {{ librarian.library_name }}</h5>
                            <p class="card-text"><i class="fas fa-user-tie icon"></i>User : {{ librarian.user.username }}</p>
                            <p class="card-text"><i class="fas fa-envelope icon"></i>Email : {{ librarian.user.email }}</p>
                            <p class="card-text"><i class="fas fa-phone-alt icon"></i>Contact : {{ librarian.librarian_phone_num }}</p>
                            <p class="card-text"><i class="fas fa-map-marked-alt icon"></i>Address : {{ librarian.librarian_address }}</p>
                            <form method="post" class="approval-actions">
                                {% csrf_token %}
                                <input type="hidden" name="librarian_id" value="{{ librarian.id }}">
                                <button type="submit" name="approval_action" value="approve" class="btn btn-approve">
                                    <i class="fas fa-check icon"></i>Approve
                                </button>
                                <button type="submit" name="approval_action" value="reject" class="btn btn-reject">
                                    <i class="fas fa-times icon"></i>Reject
                                </button>
                            </form>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>