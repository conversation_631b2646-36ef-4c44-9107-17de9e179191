<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Preload the FSEX300 font to make loader more responsive -->
  <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

  <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
  <script>
    // Fast, simple inline loader script to show loader immediately when page starts loading
    (function() {
        // Create a fast, simple loader with just LIBRAINIAN text
        var loaderHTML = `
        <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <style>
                    /* Font is preloaded for faster loading */
                    @font-face {
                        font-family: 'FSEX300';
                        src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                        font-weight: normal;
                        font-style: normal;
                        font-display: swap; /* This helps with font loading */
                    }

                    /* Simple loader text with fallback fonts */
                    .loader-text {
                        font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                        font-size: 32px;
                        color: #ffffff;
                        letter-spacing: 2px;
                        text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        white-space: nowrap;
                    }
                </style>
                <div class="loader-text">LIBRAINIAN</div>
            </div>
        </div>
        `;

        // Add loader to page immediately
        document.write(loaderHTML);

        // Remove loader when page is loaded
        window.addEventListener('load', function() {
            var loader = document.getElementById('initialLoader');
            if (loader) {
                setTimeout(function() {
                    loader.style.display = 'none';
                }, 100); // Small delay to ensure everything is rendered
            }
        });
    })();
  </script>

  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="robots" content="noindex, nofollow" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
   
  <title>Students</title>
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">


      <!-- Disable Right click -->

        

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      

  <style>
    body {
      -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif !important;
            background-color: #9bc6bf;
    }

    .form-control:focus {
      box-shadow: none;
    }

    .card {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border-radius: 1rem;
    }

    .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            /* margin-top: 10px; */
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            /* padding-top: 10px; */
            opacity: 60%;

        }

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }

    .btn_theme{
            padding: 8px 16px;
            color: #fff;
            background-color: #294282;
        }
        .btn_theme:hover{
            color: #fff;
            background-color: #28345a;
        }
        .heading {
        text-align: center;
        color: #ffffff;
        background-color: #294282;
      }
      @media (max-width: 768px) {
      .heading{
        font-size: 1.2rem;
      }
      }
  </style>
</head>

<body>

  <!-- Sidebar -->
  {% include "sidebar.html" %}

  <div class="container my-5">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-body">
            <h4 class="text-center text-white p-3 heading" style="border-radius: 1rem;">Student Update Form</h4>
            <form action="/students/doupdate/{{ std.slug }}/" method="post" enctype="multipart/form-data">
              {% csrf_token %}

              <div class="form-group row mb-3">
                <label for="unique_id" class="col-sm-3 col-form-label">Student Id</label>
                <div class="col-sm-9">
                  <input type="text" class="form-control" style="border-radius: 1rem !important;" id="unique_id" name="unique_id" value="{{ std.unique_id }}"
                    readonly />
                </div>
              </div>


              <div class="form-group row mb-3">
                <label for="courseName" class="col-sm-3 col-form-label">Course Name</label>
                <div class="col-sm-9">
                  <select class="form-control" style="border-radius: 1rem !important;" id="courseName" name="course" required>
                    {% if std.course %}
                    <option value="{{ std.course.id }}" selected>{{ std.course.name }}</option>
                    {% else %}
                    <option value="" selected>Select a course</option>
                    {% endif %}
                    {% for course in courses %}
                    <option value="{{ course.id }}" {% if std.course.id == course.id %}selected{% endif %}>
                      {{ course.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>


              <div class="form-group row mb-3">
                <label for="name" class="col-sm-3 col-form-label">Student Name</label>
                <div class="col-sm-9">
                  <input type="text" style="border-radius: 1rem !important;" class="form-control" id="name" name="name" value="{{ std.name }}" required />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="fathername" class="col-sm-3 col-form-label">Father's Name</label>
                <div class="col-sm-9">
                  <input type="text" class="form-control" style="border-radius: 1rem !important;" id="fathername" name="fathername" value="{{ std.f_name }}" />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="age" class="col-sm-3 col-form-label">Age</label>
                <div class="col-sm-3">
                  <input type="number" class="form-control" style="border-radius: 1rem !important;" id="age" name="age" value="{{ std.age }}" />
                </div>
                <label for="gender" class="col-sm-3 col-form-label">Gender</label>
                <div class="col-sm-3">
                  <select class="form-control" style="border-radius: 1rem !important;" id="gender" name="gender" required>
                    <option value="male" {% if std.gender == "male" %}selected{% endif %}>Male</option>
                    <option value="female" {% if std.gender == "female" %}selected{% endif %}>Female</option>
                    <option value="other" {% if std.gender == "other" %}selected{% endif %}>Other</option>
                  </select>
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="email" class="col-sm-3 col-form-label">Email</label>
                <div class="col-sm-9">
                  <input type="email" class="form-control" style="border-radius: 1rem !important;" id="email" name="email" value="{{ std.email }}" required />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="phone" class="col-sm-3 col-form-label">Mobile Number</label>
                <div class="col-sm-9">
                  <input type="tel" class="form-control" style="border-radius: 1rem !important;" id="phone" name="phone" value="{{ std.mobile }}" required />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="locality" class="col-sm-3 col-form-label">Address</label>
                <div class="col-sm-9">
                  <input type="text" class="form-control" style="border-radius: 1rem !important;" id="locality" name="locality" value="{{ std.locality }}"
                    required />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="city" class="col-sm-3 col-form-label">City</label>
                <div class="col-sm-3">
                  <input type="text" class="form-control" style="border-radius: 1rem !important;" id="city" name="city" value="{{ std.city }}" required />
                </div>
                <div class="form-group row mb-3">
                  <label for="stateName" class="col-sm-3 col-form-label">State</label>
                  <div class="col-sm-9">
                    <select class="form-control" id="stateName" style="border-radius: 1rem !important;" name="state" required>
                      {% if std.state %}
                      <option value="{{ std.state.id }}" selected>{{ std.state.name }}</option>
                      {% else %}
                      <option value="" selected>Select a state</option>
                      {% endif %}
                      {% for state in states %}
                      <option value="{{ state.id }}" {% if std.state and std.state.id == state.id %}selected{% endif %}>{{state.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="regfees" class="col-sm-3 col-form-label">Registration Fees</label>
                <div class="col-sm-9">
                  <input type="number" class="form-control" style="border-radius: 1rem !important;" id="regfees" name="regfees"
                    value="{{ std.registration_fee }}" required />
                </div>
              </div>

              <div class="form-group row mb-3">
                <label for="image" class="col-sm-3 col-form-label">Student Image</label>
                <div class="col-sm-9">
                  <input type="file" class="form-control-file" id="image" name="image" />
                </div>
              </div>

              <button type="submit" class="btn btn_theme w-100" style="border-radius: 1rem;">Update</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
    <!-- <p>Developed with passion by Librainian</p> -->
</div>




  </div>



  <!-- JavaScript dependencies -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

  <!-- blog_list -->
  <script>
    // Show success message and hide after 5 seconds
    $(document).ready(function () {
      $('.alert').fadeIn().delay(5000).fadeOut();
    });
  </script>

  
  <script>
    // Get the menu icon and submenu elements
    const menuIcon = document.getElementById('menu-icon');
    const submenu = document.getElementById('submenu');

    // Add click event listener to the menu icon
    menuIcon.addEventListener('click', function () {
      submenu.classList.toggle('show');
    });
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      var menuIcon = document.getElementById('menu-icon');
      var submenu = document.getElementById('submenu');

      // Toggle submenu visibility on menu icon click
      menuIcon.addEventListener('click', function () {
        if (submenu.style.display === 'block') {
          submenu.style.display = 'none';
        } else {
          submenu.style.display = 'block';
        }
      });
    });

  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      var footerSearch = document.getElementById('footer-search');
      var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

      footerSearch.addEventListener('click', function () {
        exampleModal.show();
      });
    });
  </script>
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

  <!-- Global Loader Script -->
   
</body>

</html>