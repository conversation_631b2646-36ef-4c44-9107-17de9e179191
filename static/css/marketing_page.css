body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
}

hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.profile-img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    /* margin-bottom: 15px; */
    border-radius: 50%;
    margin-left: 10px;
}

/* .form-control {
    border: none;
    outline: none;
    box-shadow: none;
    margin-left: 18px;
} */

.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

.search-wrapper {
    position: relative;
}

.search-wrapper input[type="search"] {
    padding-left: 2.5rem;
}

.search-wrapper .fa-magnifying-glass {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0b0b0b;
}

.card {
    margin-bottom: 20px;
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.marketing_page_card {
    background: #fff !important;
}

.card .card-body {
    position: relative;
    padding: 20px;
}

.card .card-body .icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.15);
}

.card .card-footer {
    background-color: transparent;
    border-top: none;
}

@media only screen and (max-width: 767px) {
    .small_p p{
    font-size: 12px !important;
}
}

.btn-danger {
    background-color: white;
    color: rgb(0, 0, 0);
}

.btn-danger:hover {
    background-color: rgb(230, 17, 17);
    color: rgb(255, 255, 255);
}



.btn-link {
    color: #000000;
    padding-left: 2px;
    margin-bottom: -12px;
}

.btn-link:hover {
    color: #000000;
    text-decoration: none;
}


.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    margin: 0 auto;
    font-size: 16px;
}

.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;

}

/* 
.footer p {
    font-size: 12px;
    padding-top: 5px;
} */
 

@media (max-width:768px) {
    .footer {
        margin-bottom: 100px;
    }

    .footer img {
        width: 60%;
    }

}

#notificationBtn {
    display: none;
}

@media (max-width: 768px) {
    .table-responsive thead {
        display: none;
    }

    tfoot {
        display: none;
    }

    .table-responsive tr {
        display: block;
        margin-bottom: 10px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        overflow: hidden;
    }

    .table-responsive td {
        display: flex;
        justify-content: space-between;
        border-top: none;
        padding: 10px;
        background-color: #f8f9fa;
    }

    .table-responsive td:before {
        content: attr(data-label);
        font-weight: bold;
        flex-basis: 45%;
        text-align: right;
        color: #495057;
    }

    .table-responsive td a {
        color: #007bff;
    }
}

.table-responsive td,
.table-responsive th {
    vertical-align: middle;
    text-align: center;
}


.form-control{
    border-radius: 1rem !important;
}


/* .card-header {
    background-color: #343a40;
    color: #fff;
} */
.card-title {
    margin: 0;
}

.icon-text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-text-wrapper i {
    font-size: 24px;
    /* Adjust the icon size as needed */
    margin-bottom: 5px;
    /* Space between icon and text */
}

.dashboard-text {
    font-size: 12px;
    /* Match the font size to the icon size */
    line-height: 24px;
    /* Set the line-height to match the icon's height */
}

.table-container {
    width: 90%;
    margin: 20px auto;
    overflow-x: auto;
}
table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;
    font-size: 16px;
}
thead th {
    position: relative;
    padding: 10px;
    background-color: #f9f9f9;
    color: #333;
    font-weight: bold;
    border-bottom: 2px solid #e0e0e0;
}
thead th i {
    margin-left: 5px;
    font-size: 12px;
    color: #aaa;
}
tbody tr:nth-child(even) {
    background-color: #f3f3f3;
}
tbody tr:hover {
    background-color: #e9e9e9;
}
tbody td {
    padding: 12px 10px;
    color: #555;
    border-bottom: 1px solid #e0e0e0;
}
tbody td input[type="checkbox"] {
    cursor: pointer;
}
.disabled {
    opacity: 0.5;
    pointer-events: none;
}
.proceed {
    background-color: #007bff;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}
/* .btn-secondary.disabled,
.btn-secondary:disabled {
    opacity: 0.65;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #6c757d;
    border-color: #6c757d;
} */