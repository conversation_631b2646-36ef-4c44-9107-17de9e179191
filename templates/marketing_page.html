    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar -->

    {% include "sidebar.html" %}

    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
           DASHBOARD / MARKETING
        </div>
    </div>

    <!-- Dashboard content -->
    <div class="container-fluid">

        <div class="row">
            <div class="col-md-12">
                <div class="card marketing_page_card">
                    <div class="card-header">
                        <h4 class="card-title">Student Data</h4>
                    </div>
                        {% if messages %}
                            {% for message in messages %}
                            <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                {{ message }}<br>
                            </div>
                            {% endfor %}
                        {% endif %}
                    <div class="card-body">
                        <!-- Desktop Filters -->
                        <div class="d-none d-md-flex align-items-center gap-3 flex-md-nowrap flex-wrap rounded" style="max-width: 100%;">
                                    <select id="courseDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Course Name</option>
                                    </select>
                                    <select id="nameDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Name</option>
                                    </select>
                                    <select id="genderDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Gender</option>
                                    </select>
                                    <select id="phoneDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Phone</option>
                                    </select>
                                    <select id="emailDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Email</option>
                                    </select>
                                    <!-- dueDropdown -->
                                    <input type="date" id="startDate" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()" />
                                    <!-- <br> -->
                                    <input type="date" id="endDate" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()" />
                        </div>

                        <!-- Mobile Filter Button -->
                        <div class="d-block d-md-none mb-3">
                            <button class="btn btn-outline-primary w-100" type="button" data-bs-toggle="collapse" data-bs-target="#mobileFilters" aria-expanded="false" aria-controls="mobileFilters">
                                <i class="fas fa-filter me-2"></i>Filters
                            </button>
                            <div class="collapse mt-3" id="mobileFilters">
                                <div class="card card-body">
                                    <div class="row g-2">
                                        <div class="col-12">
                                            <select id="mobileCourseDrop" class="form-control" onchange="applyMobileFilters()">
                                                <option value="">All Courses</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <select id="mobileGenderDrop" class="form-control" onchange="applyMobileFilters()">
                                                <option value="">All Genders</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <input type="date" id="mobileStartDate" class="form-control" placeholder="Start Date" onchange="applyMobileFilters()" />
                                        </div>
                                        <div class="col-6">
                                            <input type="date" id="mobileEndDate" class="form-control" placeholder="End Date" onchange="applyMobileFilters()" />
                                        </div>
                                        <div class="col-12">
                                            <button class="btn btn-secondary btn-sm w-100" onclick="clearMobileFilters()">Clear Filters</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Desktop Table View -->
                        <div class="table-responsive d-none d-md-block">
                            <form method="POST" action="/librarian/process-student-data/">
                                {% csrf_token %}
                                <!-- Table -->
                                <div class="mt-3">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>
                                                    <div style="display: flex; justify-content: center; align-items: center; gap: 10px">
                                                        <b></b>
                                                        <input type="checkbox" onclick="checkAll()" style="width: 20px; height: 20px" />
                                                    </div>
                                                </th>
                                                <th>Course Name <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(1)"></i></th>
                                                <th>Name <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(2)"></i></th>
                                                <th>Gender <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(3)"></i></th>
                                                <th>Phone <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(4)"></i></th>
                                                <th>Email <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(5)"></i></th>
                                                <th>Due Date <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(6, true)"></i></th>
                                            </tr>
                                        </thead>

                                        <tbody id="studentTableBody">
                                            {% for data in student_data %}
                                            <tr class="student-row"
                                                data-course-name="{{ data.student.course }}"
                                                data-name="{{ data.student.name }}"
                                                data-gender="{{ data.student.gender }}"
                                                data-phone="{{ data.student.mobile }}"
                                                data-email="{{ data.student.email }}"
                                                data-due-date="{{ data.invoice_data.due_date }}">
                                                <td><input type="checkbox" name="student_checkbox" value="{{ data.student.slug }}" onclick="checkThis(event)" style="width: 20px; height: 20px; margin-left: 0.5rem !important;" /></td>
                                                <td>{{ data.student.course }}</td>
                                                <td class="student-name">{{ data.student.name }}</td>
                                                <td>{{ data.student.gender }}</td>
                                                <td>{{ data.student.mobile }}</td>
                                                <td>{{ data.student.email }}</td>
                                                <td>{{ data.invoice_data.due_date }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row justify-content-center mt-3">
                                    <div class="col-md-3 text-center">
                                        <button type="submit" id="proceedButton" class="proceed btn btn-primary w-100 mb-2 disabled">Proceed</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Mobile Card View -->
                        <div class="d-block d-md-none">
                            <form method="POST" action="/librarian/process-student-data/">
                                {% csrf_token %}

                                <!-- Select All Button for Mobile -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="checkAllMobile()">
                                        <i class="fas fa-check-square me-1"></i>Select All
                                    </button>
                                    <span id="selectedCount" class="text-muted small">0 selected</span>
                                </div>

                                <!-- Mobile Cards Container -->
                                <div id="mobileCardsContainer">
                                    {% for data in student_data %}
                                    <div class="card mb-3 shadow-sm student-mobile-card"
                                         data-course-name="{{ data.student.course }}"
                                         data-name="{{ data.student.name }}"
                                         data-gender="{{ data.student.gender }}"
                                         data-phone="{{ data.student.mobile }}"
                                         data-email="{{ data.student.email }}"
                                         data-due-date="{{ data.invoice_data.due_date }}"
                                         style="cursor: pointer; transition: all 0.3s ease;">
                                        <div class="card-body position-relative">
                                            <!-- Checkbox -->
                                            <div class="position-absolute" style="top: 15px; right: 15px;">
                                                <input type="checkbox" name="student_checkbox" value="{{ data.student.slug }}"
                                                       onclick="checkMobileCard(event)"
                                                       style="width: 20px; height: 20px; cursor: pointer;" />
                                            </div>

                                            <!-- Student Info -->
                                            <div class="row">
                                                <div class="col-12">
                                                    <h6 class="card-title mb-2 fw-bold text-primary">{{ data.student.name }}</h6>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1"><small class="text-muted">Mobile:</small><br>
                                                    <span class="fw-semibold">{{ data.student.mobile }}</span></p>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-1"><small class="text-muted">Course:</small><br>
                                                    <span class="fw-semibold">{{ data.student.course }}</span></p>
                                                </div>
                                                <div class="col-6">
                                                    <p class="mb-0"><small class="text-muted">Gender:</small><br>
                                                    <span class="fw-semibold">{{ data.student.gender }}</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>

                                <!-- Mobile Proceed Button -->
                                <div class="row justify-content-center mt-3">
                                    <div class="col-12">
                                        <button type="submit" id="mobileProceedButton" class="proceed btn btn-primary w-100 mb-2 disabled">Proceed</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <nav class="mt-2 d-flex justify-content-center">
                            <ul class="pagination" id="pagination">
                                <!-- Pagination buttons will be inserted here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
    const rowsPerPage = 5;
    const tableBody = document.getElementById("studentTableBody");
    const pagination = document.getElementById("pagination");
    const rows = document.querySelectorAll(".student-row");
    const searchInput = document.getElementById("search");
    let currentPage = 1;
    let filteredRows = rows;

    // Function to display the table based on current page
    function displayTable(page) {
        let start = (page - 1) * rowsPerPage;
        let end = start + rowsPerPage;
        filteredRows.forEach((row, index) => {
            row.style.display = index >= start && index < end ? "" : "none";
        });
    }

    // Function to set up pagination buttons
    function setupPagination() {
        let pageCount = Math.ceil(filteredRows.length / rowsPerPage);
        pagination.innerHTML = "";

        // Add Previous Button
        let prevPage = document.createElement("li");
        prevPage.className = "page-item" + (currentPage === 1 ? " disabled" : "");
        let prevLink = document.createElement("a");
        prevLink.className = "page-link";
        prevLink.href = "#";
        prevLink.innerText = "Previous";
        prevLink.addEventListener("click", function (e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                displayTable(currentPage);
                setupPagination();
            }
        });
        prevPage.appendChild(prevLink);
        pagination.appendChild(prevPage);

        // Calculate which page numbers to show (show 4 pages + last page)
        let startPage = Math.max(1, currentPage - 2); // Show pages before current page
        let endPage = Math.min(pageCount, currentPage + 2); // Show pages after current page

        if (startPage > 1) {
            let firstPage = document.createElement("li");
            firstPage.className = "page-item";
            let firstLink = document.createElement("a");
            firstLink.className = "page-link";
            firstLink.href = "#";
            firstLink.innerText = "1";
            firstLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = 1;
                displayTable(currentPage);
                setupPagination();
            });
            firstPage.appendChild(firstLink);
            pagination.appendChild(firstPage);

            let ellipsis = document.createElement("li");
            ellipsis.className = "page-item disabled";
            let ellipsisLink = document.createElement("span");
            ellipsisLink.className = "page-link";
            ellipsisLink.innerText = "...";
            ellipsis.appendChild(ellipsisLink);
            pagination.appendChild(ellipsis);
        }

        // Add the page number buttons
        for (let i = startPage; i <= endPage; i++) {
            let pageItem = document.createElement("li");
            pageItem.className = "page-item" + (i === currentPage ? " active" : "");
            let pageLink = document.createElement("a");
            pageLink.className = "page-link";
            pageLink.href = "#";
            pageLink.innerText = i;
            pageLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = i;
                displayTable(currentPage);
                setupPagination();
            });
            pageItem.appendChild(pageLink);
            pagination.appendChild(pageItem);
        }

        // Add Ellipsis and Last Page Button
        if (endPage < pageCount) {
            let ellipsis = document.createElement("li");
            ellipsis.className = "page-item disabled";
            let ellipsisLink = document.createElement("span");
            ellipsisLink.className = "page-link";
            ellipsisLink.innerText = "...";
            ellipsis.appendChild(ellipsisLink);
            pagination.appendChild(ellipsis);

            let lastPage = document.createElement("li");
            lastPage.className = "page-item";
            let lastLink = document.createElement("a");
            lastLink.className = "page-link";
            lastLink.href = "#";
            lastLink.innerText = pageCount;
            lastLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = pageCount;
                displayTable(currentPage);
                setupPagination();
            });
            lastPage.appendChild(lastLink);
            pagination.appendChild(lastPage);
        }

        // Add Next Button
        let nextPage = document.createElement("li");
        nextPage.className = "page-item" + (currentPage === pageCount ? " disabled" : "");
        let nextLink = document.createElement("a");
        nextLink.className = "page-link";
        nextLink.href = "#";
        nextLink.innerText = "Next";
        nextLink.addEventListener("click", function (e) {
            e.preventDefault();
            if (currentPage < pageCount) {
                currentPage++;
                displayTable(currentPage);
                setupPagination();
            }
        });
        nextPage.appendChild(nextLink);
        pagination.appendChild(nextPage);
    }

    // Search function for filtering students
    function searchStudents() {
        const query = searchInput.value.toLowerCase();
        filteredRows = Array.from(rows).filter(row =>
            row.querySelector(".student-name").textContent.toLowerCase().includes(query)

        );
        console.log("Filtered Row", filteredRows)
        currentPage = 1; // Reset to the first page
        displayTable(currentPage);
        setupPagination();
    }

    // Apply the search event
    searchInput.addEventListener("input", searchStudents);

    // Initialize the table and pagination
    displayTable(currentPage);
    setupPagination();
});

        </script>

    <!-- Check Box in table -->
    <script>

        // This is the data in which selected row will come (to pass)
        let selectedRowsData = []; // Array to store data of selected rows
        let allChecked = false; // Toggle state for checkAll
        let mobileAllChecked = false; // Toggle state for mobile checkAll


        function checkAll() {
            allChecked = !allChecked; // Toggle state
            selectedRowsData = []; // Clear the array for fresh selection

            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');

            checkboxes.forEach((checkbox) => {
                checkbox.checked = allChecked; // Set all checkboxes based on toggle state
                const row = checkbox.closest('tr'); // Get the row of this checkbox
                const rowData = Array.from(row.children).slice(1).reduce((acc, cell, index) => {
                    const headers = ['Course Name', 'Name', 'Gender', 'Phone', 'Email', 'Due Date']; // Define column headers
                    acc[headers[index]] = cell.innerText; // Map headers to cell values
                    return acc;
                }, {});

                if (allChecked) {
                    selectedRowsData.push(rowData); // Add data if checked
                }
            });

            updateButtonVisibility();
            console.log("Selected rows data:", selectedRowsData);
        }

        function checkThis(event) {
            const checkbox = event.target;
            const row = checkbox.closest('tr');
            const rowData = Array.from(row.children).slice(1).reduce((acc, cell, index) => {
                const headers = ['Course Name', 'Name', 'Gender', 'Phone', 'Email', 'Due Date'];
                acc[headers[index]] = cell.innerText;
                return acc;
            }, {});

            if (checkbox.checked) {
                selectedRowsData.push(rowData); // Add to selected rows if checked
            } else {
                // Remove row from selected rows if unchecked
                selectedRowsData = selectedRowsData.filter(item => item['Name'] !== rowData['Name']);
            }

            updateButtonVisibility();
            console.log("Selected rows data:", selectedRowsData);
        }

        // Mobile card functions
        function checkAllMobile() {
            mobileAllChecked = !mobileAllChecked;
            selectedRowsData = [];

            // Only select visible cards (not filtered out)
            const visibleCards = document.querySelectorAll('.student-mobile-card:not([style*="display: none"])');

            visibleCards.forEach((card) => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                checkbox.checked = mobileAllChecked;

                if (mobileAllChecked) {
                    const rowData = {
                        'Course Name': card.getAttribute('data-course-name'),
                        'Name': card.getAttribute('data-name'),
                        'Gender': card.getAttribute('data-gender'),
                        'Phone': card.getAttribute('data-phone'),
                        'Email': card.getAttribute('data-email'),
                        'Due Date': card.getAttribute('data-due-date')
                    };
                    selectedRowsData.push(rowData);
                }
            });

            // Also uncheck any hidden cards
            const hiddenCards = document.querySelectorAll('.student-mobile-card[style*="display: none"]');
            hiddenCards.forEach((card) => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
            });

            updateMobileButtonVisibility();
            updateSelectedCount();
            console.log("Selected mobile cards data:", selectedRowsData);
        }

        function checkMobileCard(event) {
            event.stopPropagation(); // Prevent card click from interfering
            const checkbox = event.target;
            const card = checkbox.closest('.student-mobile-card');

            const rowData = {
                'Course Name': card.getAttribute('data-course-name'),
                'Name': card.getAttribute('data-name'),
                'Gender': card.getAttribute('data-gender'),
                'Phone': card.getAttribute('data-phone'),
                'Email': card.getAttribute('data-email'),
                'Due Date': card.getAttribute('data-due-date')
            };

            if (checkbox.checked) {
                // Check if this item is already in the array to avoid duplicates
                const existingIndex = selectedRowsData.findIndex(item => item['Name'] === rowData['Name']);
                if (existingIndex === -1) {
                    selectedRowsData.push(rowData);
                }
            } else {
                // Remove from selected rows if unchecked
                selectedRowsData = selectedRowsData.filter(item => item['Name'] !== rowData['Name']);
            }

            updateMobileButtonVisibility();
            updateSelectedCount();
            console.log("Selected mobile cards data:", selectedRowsData);
        }

        function updateButtonVisibility() {
            const button = document.getElementById('proceedButton');
            if (selectedRowsData.length > 0) {
                button.classList.remove('disabled'); // Enable button
            } else {
                button.classList.add('disabled'); // Disable button
            }
        }

        function updateMobileButtonVisibility() {
            const button = document.getElementById('mobileProceedButton');
            if (selectedRowsData.length > 0) {
                button.classList.remove('disabled'); // Enable button
            } else {
                button.classList.add('disabled'); // Disable button
            }
        }

        function updateSelectedCount() {
            const countElement = document.getElementById('selectedCount');
            if (countElement) {
                countElement.textContent = `${selectedRowsData.length} selected`;
            }
        }

    </script>

    <!-- Sorting of body -->
    <script>
        let sortOrder = {}; // To keep track of the sorting order for each column

        function sortTable(columnIndex, isDate = false) {
            const table = document.querySelector("table tbody");
            const rows = Array.from(table.rows);

            // Toggle sorting order
            sortOrder[columnIndex] = !sortOrder[columnIndex];

            rows.sort((rowA, rowB) => {
                let cellA = rowA.cells[columnIndex].textContent;
                let cellB = rowB.cells[columnIndex].textContent;

                if (isDate) {
                    cellA = new Date(cellA);
                    cellB = new Date(cellB);
                } else if (!isNaN(cellA) && !isNaN(cellB)) {
                    // If values are numbers, parse them
                    cellA = parseFloat(cellA);
                    cellB = parseFloat(cellB);
                }

                // Determine the sort order (ascending or descending)
                if (sortOrder[columnIndex]) {
                    return cellA > cellB ? 1 : -1;
                } else {
                    return cellA < cellB ? 1 : -1;
                }
            });

            // Append the sorted rows back to the table
            rows.forEach(row => table.appendChild(row));
        }
    </script>

    <!-- Filter dropdown -->
    <script>
        // Helper function to parse dates in 'DD-MM-YYYY' format
        function parseDate(dateString) {
            const parts = dateString.split('-');
            // Return new Date in 'YYYY-MM-DD' format
            return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
        }

        function applyFilters() {
            const courseDropdown = document.getElementById("courseDropdown").value;
            const nameDropdown = document.getElementById("nameDropdown").value;
            const genderDropdown = document.getElementById("genderDropdown").value;
            const phoneDropdown = document.getElementById("phoneDropdown").value;
            const emailDropdown = document.getElementById("emailDropdown").value;
            const startDateInput = document.getElementById("startDate").value;
            const endDateInput = document.getElementById("endDate").value;

            // Parse start and end dates
            const startDate = startDateInput ? new Date(startDateInput) : null;
            const endDate = endDateInput ? new Date(endDateInput) : null;

            const rows = document.querySelectorAll("tbody tr");

            rows.forEach(row => {
                const course = row.getAttribute("data-course-name");
                const name = row.getAttribute("data-name");
                const gender = row.getAttribute("data-gender");
                const phone = row.getAttribute("data-phone");
                const email = row.getAttribute("data-email");
                const dueDate = parseDate(row.getAttribute("data-due-date")); // Parse using the helper function

                // Filter conditions
                const courseMatch = courseDropdown === "" || courseDropdown === course;
                const nameMatch = nameDropdown === "" || nameDropdown === name;
                const genderMatch = genderDropdown === "" || genderDropdown === gender;
                const phoneMatch = phoneDropdown === "" || phoneDropdown === phone;
                const emailMatch = emailDropdown === "" || emailDropdown === email;

                // Date range match
                const dateMatch =
                    (startDate === null || dueDate >= startDate) &&
                    (endDate === null || dueDate <= endDate);

                // Display rows that match all conditions
                row.style.display = (courseMatch && nameMatch && genderMatch && phoneMatch && emailMatch && dateMatch) ? "" : "none";
            });
        }

        // Mobile filter functions
        function applyMobileFilters() {
            const courseDropdown = document.getElementById("mobileCourseDrop").value;
            const genderDropdown = document.getElementById("mobileGenderDrop").value;
            const startDateInput = document.getElementById("mobileStartDate").value;
            const endDateInput = document.getElementById("mobileEndDate").value;

            // Parse start and end dates
            const startDate = startDateInput ? new Date(startDateInput) : null;
            const endDate = endDateInput ? new Date(endDateInput) : null;

            const cards = document.querySelectorAll(".student-mobile-card");

            cards.forEach(card => {
                const course = card.getAttribute("data-course-name");
                const gender = card.getAttribute("data-gender");
                const dueDate = parseDate(card.getAttribute("data-due-date"));

                // Filter conditions
                const courseMatch = courseDropdown === "" || courseDropdown === course;
                const genderMatch = genderDropdown === "" || genderDropdown === gender;

                // Date range match
                const dateMatch =
                    (startDate === null || dueDate >= startDate) &&
                    (endDate === null || dueDate <= endDate);

                // Display cards that match all conditions
                card.style.display = (courseMatch && genderMatch && dateMatch) ? "" : "none";

                // If card is hidden, uncheck it and remove from selection
                if (card.style.display === "none") {
                    const checkbox = card.querySelector('input[type="checkbox"]');
                    if (checkbox && checkbox.checked) {
                        checkbox.checked = false;
                        const cardName = card.getAttribute("data-name");
                        selectedRowsData = selectedRowsData.filter(item => item['Name'] !== cardName);
                    }
                }
            });

            // Reset select all state
            mobileAllChecked = false;

            // Update UI
            updateMobileButtonVisibility();
            updateSelectedCount();
        }

        function clearMobileFilters() {
            document.getElementById("mobileCourseDrop").value = "";
            document.getElementById("mobileGenderDrop").value = "";
            document.getElementById("mobileStartDate").value = "";
            document.getElementById("mobileEndDate").value = "";

            // Show all cards
            const cards = document.querySelectorAll(".student-mobile-card");
            cards.forEach(card => {
                card.style.display = "";
            });

            // Reset select all state
            mobileAllChecked = false;

            // Update UI
            updateMobileButtonVisibility();
            updateSelectedCount();
        }

    </script>

    <!-- Foot of table -->
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            populateDropdowns();
            populateMobileDropdowns();
        });

        function populateDropdowns() {
            const rows = document.querySelectorAll("tbody tr");
            const uniqueValues = {
                course: new Set(),
                name: new Set(),
                gender: new Set(),
                phone: new Set(),
                email: new Set()
            };

            // Collect unique values from each row
            rows.forEach(row => {
                uniqueValues.course.add(row.cells[1].textContent.trim());
                uniqueValues.name.add(row.cells[2].textContent.trim());
                uniqueValues.gender.add(row.cells[3].textContent.trim());
                uniqueValues.phone.add(row.cells[4].textContent.trim());
                uniqueValues.email.add(row.cells[5].textContent.trim());
            });

            // Populate each dropdown with unique values
            addOptionsToDropdown(uniqueValues.course, "courseDropdown");
            addOptionsToDropdown(uniqueValues.name, "nameDropdown");
            addOptionsToDropdown(uniqueValues.gender, "genderDropdown");
            addOptionsToDropdown(uniqueValues.phone, "phoneDropdown");
            addOptionsToDropdown(uniqueValues.email, "emailDropdown");
        }

        function populateMobileDropdowns() {
            const cards = document.querySelectorAll(".student-mobile-card");
            const uniqueValues = {
                course: new Set(),
                gender: new Set()
            };

            // Collect unique values from each card
            cards.forEach(card => {
                uniqueValues.course.add(card.getAttribute('data-course-name'));
                uniqueValues.gender.add(card.getAttribute('data-gender'));
            });

            // Populate mobile dropdowns
            addOptionsToDropdown(uniqueValues.course, "mobileCourseDrop");
            addOptionsToDropdown(uniqueValues.gender, "mobileGenderDrop");
        }

        function addOptionsToDropdown(valuesSet, dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                valuesSet.forEach(value => {
                    const option = document.createElement("option");
                    option.value = option.textContent = value;
                    dropdown.appendChild(option);
                });
            }
        }
    </script>


        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">            <!-- <p>Developed with passion by Librainian</p> -->
        </div>
    </div>


    <!-- Custom CSS for Mobile Cards -->
    <style>
        .student-mobile-card {
            border-radius: 12px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .student-mobile-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            border-color: #6f42c1;
        }

        .student-mobile-card:active {
            transform: translateY(0);
        }

        .student-mobile-card .card-body {
            padding: 1rem;
        }

        .student-mobile-card .card-title {
            font-size: 1.1rem;
            margin-bottom: 0.75rem;
        }

        .student-mobile-card p {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .student-mobile-card small {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .student-mobile-card .fw-semibold {
            font-weight: 600;
            color: #333;
        }

        /* Filter button styling */
        .btn-outline-primary {
            border-color: #6f42c1;
            color: #6f42c1;
        }

        .btn-outline-primary:hover {
            background-color: #6f42c1;
            border-color: #6f42c1;
        }

        /* Selected count styling */
        #selectedCount {
            font-weight: 500;
            color: #6f42c1;
        }

        /* Mobile filter card styling */
        #mobileFilters .card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .student-mobile-card .card-body {
                padding: 0.75rem;
            }

            .student-mobile-card .card-title {
                font-size: 1rem;
            }

            .student-mobile-card p {
                font-size: 0.85rem;
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>
</html>