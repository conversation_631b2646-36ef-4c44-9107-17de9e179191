from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from Library.views import send_sms
import threading, requests, os

def send_dynamic_email(subject, template_name, context, to_email):
    from_email = settings.EMAIL_HOST_USER

    # Render the HTML template and convert it to plain text
    html_content = render_to_string(template_name, context)
    text_content = strip_tags(html_content)

    # Use threading to send email asynchronously
    email_thread = threading.Thread(
        target=send_email_thread,
        args=(subject, from_email, to_email, html_content, text_content),
    )
    email_thread.start()


def send_email_thread(subject, from_email, to_email, html_content, text_content):
    try:

        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=from_email,
            to=[to_email],
        )
      

        email.attach_alternative(html_content, "text/html")
        email.send()
    except Exception as e:
        print(f"Failed to send email: {str(e)}")


def send_email_async(subject, html_content, from_email, to_email):
    try:
        msg = EmailMultiAlternatives(
            subject, strip_tags(html_content), from_email, [to_email]
        )
        msg.attach_alternative(html_content, "text/html")
        msg.send()
    except Exception as e:
        print(f"Error sending email: {str(e)}")

template_content = {
        "getting launched":{
            "template_id": 174195,
            "content": "Dear {#var#} Shashtrath the mock test app is getting launched on {#var#}. Click here to exlore whole new world. shashtrath.com Regards SASTRH",
            },
        "Experience enhanced features":{
            "template_id": 174168,
            "content": "Dear {#var#} Experience enhanced features designed to boost your exam preparation. Try Shashtrath today! shashtrath.com Regards SASTRH",
            },
        "services fees":{
            "template_id": 173593,
            "content": "Dear {#var#}, Your library fee at {#var#} is due. Please pay today to continue enjoying our services and resources. LBRIAN",
            },
        "library fee":{
            "template_id": 173446,
            "content": "Dear, {#var#} Your library fee is due. Please pay today to continue enjoying our services and resources. LBRIAN",
            },
        "special discount":{
            "template_id": 173385,
            "content": "Hi Student, enjoy a special discount on your {#var#} examination forms at {#var#}. Apply now and save {#var#} before {#var#} LBRIAN",
            },
        "RENEWAL FEE":{
            "template_id": 171999,
            "content": "Dear {#var#}, your subscription plan {#var#} for mock series ended on {#var#}. Please pay the renewal fee to continue using Shashtrarth PINAK VENTURE",
            },
        "otp template new ap Shashtrarth":{
            "template_id": 171853,
            "content": "Dear student, Welcome to Shashtrarth, {#var#} is your OTP for registration. PINAK VENTURE",
            },
        "Library Subscription":{
            "template_id": 170593,
            "content": "Dear Customer, Your Library Subscription of Rs {#var#} has been generated. Check details: {#var#}{#var#} LBRIAN",
            },
        "invoice generated":{
            "template_id": 170456,
            "content": "Dear {#var#}, Your invoice of {#var#} has been generated. Check details: librainian.com/students/s/{#var#}",
            },
        "invoice new":{
            "template_id": 168803,
            "content": "Dear student, your fee for {#var#} collected check invoice https://librainian.com/students/s/{#var#} - LIBRARIAN",
            },
        "otp reg":{
            "template_id": 165734,
            "content": "Dear Student, Thank you so much for choosing librainian.com, Your OTP to get registered {#var#}. Regards PINAK VENTURE LBRIAN",
            },
        "partner in growth":{
            "template_id": 165731,
            "content": "Hi {#var#}, Thank you so much for choosing Librainian App – Your partner in growth, Your OTP to register you Library is: {#var#}. PNKVEN",
            },
}


def send_bulk_sms(numbers, message, template_id):
    url = "https://www.bulksmsplans.com/api/send_sms"  # SMS API URL
    payload = {
        "api_id": settings.BULKSMS_API_ID,
        "api_password": settings.BULKSMS_API_PASSWORD,
        "sms_type": "Transactional",
        "sms_encoding": "3",
        "sender": settings.BULKSMS_SENDER,
        "number": numbers,  # Join multiple numbers with a comma
        "message": message,
        "template_id": template_id
    }

    headers = {}
    try:
        response = requests.post(url, data=payload, headers=headers)
        return response.json()  # Assuming API returns JSON response
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}


    


