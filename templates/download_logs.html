<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
   <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --light-bg: #f8f9fa;
            --border-radius: 8px;
        }

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            background-color: #f5f5f5;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding: 40px 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-container {
            width: 100%;
            max-width: 1200px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .header {
            background: var(--primary-color);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: 500;
            margin: 0;
            text-align: center;
            letter-spacing: 0.5px;
        }

        .back-button {
            position: absolute;
            left: 25px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 15px;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .content {
            padding: 0 30px 30px;
        }

        .log-section {
            background: var(--light-bg);
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .section-title {
            color: var(--primary-color);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #eee;
        }

        .btn-download {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
        }

        .btn-download:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
            color: white;
        }

        .btn-download i {
            font-size: 20px;
        }

        /* Custom scrollbar */
        .log-section::-webkit-scrollbar {
            width: 8px;
        }

        .log-section::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .log-section::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .log-section::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }
    </style>
</head>
<body>


    <div class="main-container">
        <div class="header">
            <a href="/librarycommander/dashboard/" class="back-button">
                <i class="bi bi-arrow-left"></i>
                Back to Dashboard
            </a>
            <h1>System Logs Dashboard</h1>
        </div>
        
        <div class="content">
            <div class="row g-4">
                <!-- Access Log Section -->
                <div class="col-md-6">
                    <h2 class="section-title">Access Log</h2>
                    <div class="log-section">
                        {% for line in access_log_tail %}
                            {{ line }}<br>
                        {% endfor %}
                    </div>
                    <a href="{% url 'download_log' 'access' %}" class="btn btn-download">
                        <i class="bi bi-file-earmark-arrow-down"></i>
                        Download Access Log
                    </a>
                </div>
                
                <!-- Error Log Section -->
                <div class="col-md-6">
                    <h2 class="section-title">Error Log</h2>
                    <div class="log-section">
                        {% for line in error_log_tail %}
                            {{ line }}<br>
                        {% endfor %}
                    </div>
                    <a href="{% url 'download_log' 'error' %}" class="btn btn-download">
                        <i class="bi bi-file-earmark-arrow-down"></i>
                        Download Error Log
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.9.1/font/bootstrap-icons.min.css" rel="stylesheet">
</body>
</html>