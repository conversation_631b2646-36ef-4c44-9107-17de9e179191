<!DOCTYPE html>
<html lang="en">
<head>

    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Librainian - Modern Library Management System | Simple, Fast, and Accessible</title>
    <meta name="description" content="Librainian is the #1 Library Management System for modern librarians. Streamline operations, track expenses, manage members, access our blog resources, and find libraries near you. Join 600+ libraries already using our platform!">
    <meta name="keywords" content="library management system, LMS, digital library management, library blog, find libraries near me, library search engine, library registration, library community, library analytics, library membership management, library resources, library software, library automation, library database">
    <meta name="robots" content="index,follow,max-image-preview:large">
    <meta http-equiv="content-language" content="en">
    <link rel="canonical" href="https://www.librainian.com/">
    <link rel="manifest" href="{% static 'js/manifest.json' %}">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/img/favicon-16x16.png">

    <!-- Preconnect to External Domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- Mobile App Capability -->
    <meta name="theme-color" content="#042299">
    <meta property="og:title" content="Librainian - Modern Library Management System | Simple, Fast, and Accessible">
    <meta property="og:description" content="Librainian is the #1 Library Management System for modern librarians. Streamline operations, track expenses, manage members, access our blog resources, and find libraries near you. Join 600+ libraries already using our platform!">
    <meta property="og:url" content="https://www.librainian.com/">
    <meta property="og:image" content="https://www.librainian.com/static/img/link_cover.jpg">

    <meta itemprop="author" content="Team Librainian">
    <meta itemprop="datePublished" content="01.07.2024">
    <meta itemprop="dateModified" content="15.07.2024">

    <!-- Twitter Card data -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Librainian - Modern Library Management System | Simple, Fast, and Accessible">
    <meta name="twitter:description" content="Librainian is the #1 Library Management System for modern librarians. Streamline operations, track expenses, manage members, access our blog resources, and find libraries near you. Join 600+ libraries already using our platform!">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/link_cover.jpg">

    <!-- Additional Open Graph data -->
    <meta property="og:site_name" content="Librainian">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="en_US">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Pinterest -->
    <meta name="pinterest-rich-pin" content="true">

    <!-- WhatsApp Preview -->
    <meta property="og:image:alt" content="Librainian - Library Management System">

    <!-- PWA Settings -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Librainian">
    <link rel="apple-touch-icon" href="/static/img/apple-touch-icon.png">
    <link rel="mask-icon" href="/static/img/safari-pinned-tab.svg" color="#042299">
    <link rel="stylesheet" href="/static/css/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Librainian",
        "applicationCategory": "BusinessApplication",
        "applicationSubCategory": "Library Management Software",
        "operatingSystem": "Web, Android",
        "offers": {
            "@type": "Offer",
            "price": "399",
            "priceCurrency": "INR"
        },
        "description": "Take your library online with our all-in-one management tool. Gain control, understand performance, and forecast your growth. Access our blog resources and join a community of 600+ libraries.",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "600"
        },
        "url": "https://www.librainian.com/",
        "author": {
            "@type": "Organization",
            "name": "Pinak Venture"
        },
        "datePublished": "2024-07-01",
        "dateModified": "2025-05-04"
    }
    </script>

    <!-- Organization Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Librainian",
        "url": "https://www.librainian.com/",
        "logo": "https://www.librainian.com/static/img/figma_files/logo 1.png",
        "sameAs": [
            "https://www.facebook.com/librainian",
            "https://www.twitter.com/librainian",
            "https://www.linkedin.com/company/librainian"
        ]
    }
    </script>

    <!-- Blog Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": "Librainian Blog",
        "description": "Insights, updates, and knowledge hub for library management, security features, and best practices for modern librarians.",
        "url": "https://librainian.com/blogs/p/",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/figma_files/logo 1.png"
            }
        }
    }
    </script>

    <!-- Library Search Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "Library Search",
        "description": "Find libraries near you with our search engine. Browse through 600+ registered libraries across the country.",
        "provider": {
            "@type": "Organization",
            "name": "Librainian"
        },
        "url": "https://www.librainian.com/librarian/library-list/"
    }
    </script>


    <script>
        if ('serviceWorker' in navigator) {
          window.addEventListener('load', function() {
            navigator.serviceWorker.register("{% static 'js/service_worker.js' %}")
              .then(function(registration) {
                console.log('Service Worker registered with scope:', registration.scope);
              })
              .catch(function(error) {
                console.error('Service Worker registration failed:', error);
              });
          });
        }
      </script>


    <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

    


    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            background-color: #f5f0f0;
        }

        .card {
      text-align: center;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      background: linear-gradient(135deg, #0e0e0e, #6610f2);
      color: #fff;
    }
    .increment_img_container img {
      width: 12rem;
      height: 12rem;
      margin-bottom: 10px;
    }
    .number {
      font-size: 2.5rem;
      font-weight: bold;
    }

        /* Navbar Styling */
        #navbarSupportedContent {
            flex-grow: 1;
        }

        @media only screen and (min-width: 1023px) {
            .navbar-brand {
            margin-left: -1rem;
            margin-right: auto;
        }
        }

        .navbar-brand {
            margin-left: auto;
            margin-right: auto;
        }

        .navbar-nav {
            margin-left: auto;
            margin-right: auto;
        }

        .navbar-collapse {
            justify-content: space-between;
        }

        /* Ensure Bootstrap mobile menu displays properly */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                position: absolute;
                top: 100%;
                right: 0;
                left: 0;
                z-index: 1000;
                background-color: white;
                padding: 1rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                border-radius: 0 0 0.25rem 0.25rem;
            }
        }

        .dropdown-menu {
            margin-left: 0;
        }

        /* Register Section Styling */
        .register-section {
            padding: 30px 0;
            color: #fff;
        }

        @media only screen and (max-width: 767px) {
            .register-section {
                padding: 1rem;
            }

            .wrapper span {
            font-size: 0.9rem;
        }
        .card .img {
    background: rgb(197, 218, 197);
    width: 150px;
    height: 150px;
    margin-bottom: 0;
    margin-top: 0;
    border-radius: 50%;
}

#left #right {
    padding: 0;
    margin: 0;
    display: none !important;
}

        .card .img img {
            width: 7rem;
            height: 7rem;
        }
        }

        .register-card {
            background-color: #fff;
            color: #333;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .register-card:hover {
            transform: translateY(-10px);
        }

        .register-heading {
            font-weight: bold;
            font-size: 1.75rem;
        }

        .register-paragraph {
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .register-button {
            margin-top: 20px;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: bold;
            background: #fff;
            border: none;
            color: #6610f2;
            border-radius: 30px;
            border: 1px solid #fff;
            transition: background 0.3s ease;
        }

        .register-button:hover {
            background:rgb(42, 12, 207);
            color: #fff;
            border: 1px solid #fff;

        }


        .get-started-button {
            margin-top: 20px;
            padding: 12px 30px;
            font-size: 1.5rem;
            font-weight: bold;
            background: #6610f2;
            border: none;
            color: #fff;
            border-radius: 30px;
            border: 1px solid #fff;
            transition: background 0.3s ease;
        }

        .get-started-button:hover {
            background:rgb(42, 12, 207);
            color: #fff;
            border: 1px solid #fff;

        }


        .register-image img {
            max-width: 100%;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .menu {
    display: none;
    flex-direction: column;
}

.menu.open {
    display: flex;
}

/* Button styling for mobile */
#menu-toggle {
    display: block;
    cursor: pointer;
}

/* Desktop: Ensure the menu is always visible */
@media (min-width: 768px) {
    .menu {
        display: flex;
        flex-direction: row;
    }
    #menu-toggle {
        display: none;
    }
}

@media (max-width: 768px) {
    .link-wrapper .links {
        margin-left: 50px;
        padding: 0.5rem 0rem;
    }
    .cyber_desk {
        display: none !important;
        height: 0px;
        width: 0px;
    }

    .cyber_mob{
        max-width: 100%;
        height: auto;
        margin-bottom: 7rem;
        display: inline-block;
    }
}

@media only screen and (min-width: 768px) {
    .cyber_desk {
        width: 100%;
        display: block;
    }

    .cyber_mob{
        display: none;
    }
}



    </style>

    <style>
    /* Normal text color */
    .navbar-nav .nav-link {
        color: black !important;
        font-weight: 400;
    }

    /* Text color on hover */
    .navbar-nav .nav-link:hover {
        color: grey !important;
    }

    /* User icon styling */
    .fa-user-circle {
        color: #042299;
        transition: color 0.3s ease;
    }

    .fa-user-circle:hover {
        color: #6610f2 !important;
    }

    /* Library Search Section Styling */
    .library-search-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin: 40px 0;
    }

    .library-search-content h2 {
        color: #042299;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .library-search-content p {
        color: #555;
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .library-features {
        list-style: none;
        padding-left: 0;
        margin: 25px 0;
    }

    .library-features li {
        margin-bottom: 12px;
        font-size: 1.05rem;
        color: #333;
    }

    .library-features .fas {
        color: #042299;
    }

    .search-libraries-btn {
        background-color: #042299;
        border: none;
        padding: 10px 20px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .search-libraries-btn:hover {
        background-color: #031b77;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .library-search-image {
        max-height: 400px;
        transition: transform 0.3s ease;
    }

    .library-search-image:hover {
        transform: scale(1.02);
    }

    @media (max-width: 768px) {
        .library-search-content {
            text-align: center;
            margin-bottom: 30px;
        }

        .library-features li {
            text-align: left;
        }
    }

    /* Blog Section Styling */
    .blog-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin: 40px 0;
    }

    .blog-subtitle {
        color: #042299;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .blog-description {
        color: #555;
        font-size: 1.1rem;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto 30px;
    }

    .blog-card {

        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .blog-img-container {
        position: relative;
        overflow: hidden;
    }

    .blog-img {
        height: 200px;
        width: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .blog-card:hover .blog-img {
        transform: scale(1.05);
    }

    .blog-category {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: #042299;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .blog-content {
        padding: 20px;
    }

    .blog-content h3 {
        color: #333;
        font-size: 1.3rem;
        margin-bottom: 12px;
        font-weight: 600;
        line-height: 1.4;
    }

    .blog-content p {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .blog-meta {
        display: flex;
        justify-content: space-between;
        color: #888;
        font-size: 0.85rem;
        margin-bottom: 15px;
    }

    .blog-meta span {
        display: flex;
        align-items: center;
    }

    .blog-meta i {
        margin-right: 5px;
    }

    .blog-btn {
        background-color: transparent;
        color: #042299;
        border: 1px solid #042299;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
    }

    .blog-btn i {
        margin-left: 5px;
        transition: transform 0.3s ease;
    }

    .blog-btn:hover {
        background-color: #042299;
        color: white;
    }

    .blog-btn:hover i {
        transform: translateX(3px);
    }

    .view-all-btn {
        background-color: #042299;
        border: none;
        padding: 10px 25px;
        font-size: 1.1rem;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .view-all-btn:hover {
        background-color: #031b77;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .blog-card {
            margin-bottom: 30px;
        }
    }

    /* Blog Overview Styling */
    .blog-overview-card {

        overflow: hidden;
        padding: 30px;
        margin-bottom: 20px;
    }

    .blog-overview-content h3 {
        color: #042299;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .blog-topics {
        list-style: none;
        padding-left: 0;
        margin: 25px 0;
    }

    .blog-topics li {
        margin-bottom: 12px;
        font-size: 1.05rem;
        color: #333;
    }

    .blog-topics .fas {
        color: #042299;
    }

    .blog-main-btn {
        background-color: #042299;
        border: none;
        padding: 10px 20px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .blog-main-btn:hover {
        background-color: #031b77;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .blog-overview-image {
        max-height: 300px;
        transition: transform 0.3s ease;
    }

    .blog-overview-image:hover {
        transform: scale(1.02);
    }

    /* Register Section Styling */
    .register-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin: 40px 0;
    }

    .register-content h2 {
        color: #042299;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .register-content p {
        color: #555;
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .register-features {
        list-style: none;
        padding-left: 0;
        margin: 25px 0;
    }

    .register-features li {
        margin-bottom: 12px;
        font-size: 1.05rem;
        color: #333;
    }

    .register-features .fas {
        color: #042299;
    }

    .register-btn {
        background-color: #042299;
        border: none;
        padding: 10px 20px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .register-btn:hover {
        background-color: #031b77;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .register-image {
        max-height: 400px;
        transition: transform 0.3s ease;
    }

    .register-image:hover {
        transform: scale(1.02);
    }

    @media (max-width: 768px) {
        .register-content {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-features li {
            text-align: left;
        }
    }
</style>


</head>
<body style="overflow-x: hidden;">



    <nav id="mynav" class="navbar navbar-expand-lg navbar-light bg-light" style="background-color: #fff;">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand ms-3" href="/">
                <img src="/static/img/logo_trans_name.png" alt="Librainian" class="img-fluid" style="max-height: 60px;">
            </a>

            <!-- Toggle button (mobile) -->
            <button class="navbar-toggler me-3" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navbar content -->
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <!-- Left-aligned nav items -->
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="#home" onclick="closeMenu()">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services" onclick="closeMenu()">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials" onclick="closeMenu()">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing" onclick="closeMenu()">Pricing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#download" onclick="closeMenu()">Download</a>
                    </li>

                    <!-- Us dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="usDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Us
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="usDropdown">
                            <li><a class="dropdown-item" href="#about" onclick="closeMenu()">About</a></li>
                            <li><a class="dropdown-item" href="#contact" onclick="closeMenu()">Contact</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Right-aligned user dropdown -->
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="loginDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Login">
                            <i class="fas fa-user-circle fa-lg me-1"></i> <span class="d-none d-lg-inline">Account</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="loginDropdown">
                            <li><a class="dropdown-item" href="/librarian/login/">Librarian</a></li>
                            <li><a class="dropdown-item" href="/sublibrarian/login/">Sub-Librarian</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>



    <!-- <nav id="mynav" class="navbar navbar-expand-lg navbar-light bg-light" style="background-color: #fff;">
        <div class="container">
            <a class="navbar-brand ml-5" href="#">
                <img src="/static/img/logo_trans_name.png" alt="Librainian" class="img-fluid" style="max-width: 100%; height: auto; max-height: 60px;">
            </a>
            <button class="navbar-toggler mr-5" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse justify-content-end" id="navbarSupportedContent">
                <ul class="navbar-nav mr-auto ">
                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#home" onclick="closeMenu()" >Home</a>
                    </li>

                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#services" onclick="closeMenu()">Services</a>
                    </li>
                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#testimonials" onclick="closeMenu()">Testimonials</a>
                    </li>

                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#pricing" onclick="closeMenu()">Pricing</a>
                    </li>
                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="/librarian/library-list/" onclick="closeMenu()">Find Libraries</a>
                    </li>
                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#blog" onclick="closeMenu()">Blog</a>
                    </li>
                    <li class="nav-item link-wrapper">
                        <a class="nav-link links" href="#download" onclick="closeMenu()">Download</a>

                    <li class="nav-item link-wrapper dropdown p-0">
                        <a class="nav-link p-0 dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Us</a>
                        <ul class="dropdown-menu ml-3">
                            <li><a class="nav-link links" href="#about" onclick="closeMenu()" >About</a></li>
                            <li class="nav-item link-wrapper">
                                <a class="nav-link links" href="#contact" onclick="closeMenu()">Contact</a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item link-wrapper dropdown p-0">
                        <a class="nav-link p-0 dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Login">
                            <i class="fas fa-user-circle fa-lg"></i>
                        </a>
                        <ul class="dropdown-menu ml-3">
                            <li><a class="dropdown-item" href="/librarian/login/">Librarian</a></li>
                            <li><a class="dropdown-item" href="/sublibrarian/login/">Sub-Librarian</a></li>
                        </ul>
                    </li>
                </ul>
                <form class="form-inline my-2 my-lg-0">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0 d-flex align-items-center">
                    </ul>
                </form>
            </div>
        </div>
    </nav> -->


    <main class="fadeInUp-animation">
      <section id="home">
        <div class="container mt-5">
            <div class="row align-items-center">
                <!-- Text Section -->
                <div class="col-md-6 text-center text-md-start">
                    <div class="home-text">
                        <h1>"Revolutionizing</h1>
                        <h1>Library Management -</h1>
                        <h1 class="diff"><span style="color: #042299;">Simple, Fast, and Accessible</span></h1>
                        <h1>for the Modern</h1>
                        <h1>Librarian."  </h1>
                        <a href="/librarian/signup/" class="btn get-started-button"> Get Started </a>

                    </div>
                </div>
                <!-- Image Section -->
                <div class="col-md-6 text-center">
                    <img src="/static/img/figma_files/avataar.png" class="img-fluid home-image" alt="Librainian App">
                </div>
            </div>
        </div>
    </section>


    <div class="container my-5">
        <div class="row g-4">

          <!-- Library Card -->
          <div class="col-md-4">
            <div class="card">
             <div class="increment_img_container">
                <img src="/static/img/figma_files/library_register.png" alt="Invoices Per Day">
             </div>
              <div class="number" id="libraries">600+</div>
              <p class="text-white">Registered Libraries</p>
            </div>
          </div>

          <div class="col-md-4">
            <div class="card ">
             <div class="increment_img_container">
                <img src="/static/img/figma_files/bill.png" alt="Invoices Per Day">
             </div>
              <div class="number" id="invoices">0</div>
              <p class="text-white">Invoices Per Day</p>
            </div>
          </div>
          <!-- New Students Card -->
          <div class="col-md-4">
            <div class="card bg-light">
                <div class="increment_img_container">
                    <img src="/static/img/figma_files/Library.png" alt="New Students Per Day">
                 </div>
              <div class="number" id="students">0</div>
              <p class="text-white">New Students Per Day</p>
            </div>
          </div>
        </div>
      </div>


        <section id="about">
            <p class="main-text">About Us</p>
            <section class="about-us">
                <div class="contents">
                    <div class="contents-left">
                        <div>
                            <p>At Librainian, we love creating new ideas. From the start, our goal has been to change how library management software works.</p>
                        <div class="icon-text">
                            <img src="/static/img/figma_files/lightbulb-icon.png" alt="Lightbulb Icon" class="icon">
                        </div>
                        </div>
                        <div>
                            <p>Every feature, from start to finish, is carefully designed to make a librarian's work easier and keep everything clear and simple.</p>
                        <div class="icon-text">
                            <img src="/static/img/figma_files/ruler-icon.png" alt="Ruler Icon" class="icon">
                        </div>
                        </div>
                    </div>

                    <div class="contents-center">
                        <img src="/static/img/figma_files/Teamwork Image.png" alt="Teamwork Image" class="center-image">
                    </div>

                    <div class="contents-right">
                        <div>
                            <p>Our name, Librainian, shows our promise to build a tool that makes a librarian’s job easier. It's simple, fast, reliable, and affordable.</p>
                        <div class="icon-text">
                            <img src="/static/img/figma_files/wrench-icon.png" alt="Wrench Icon" class="icon">
                        </div>
                        </div>
                        <div>
                            <p>Join us on our journey to change the growing library industry with Librainian. Together, we’ll shape the future of libraries.</p>
                        <div class="icon-text">
                            <img src="/static/img/figma_files/bookmark-icon.png" alt="Bookmark Icon" class="icon">
                        </div>
                        </div>
                    </div>

                </div>
            </section>
        </section>


        <section id="services">
            <p class="main-text">Services We Provide</p>
            <div class="wrapper">
                <i id="left" class="fa-solid  fas fa-angle-left"></i>
                <ul class="carousel">
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser1.png" alt="service 1" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Digital Library Management</h2>
                            <div class="container"><span>Librainian is a centralized system for member databases and resources, ensuring seamless access across devices and optimized library efficiency.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser2.png" alt="service 2" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Predictive Analytics</h2>
                            <div class="container"><span>The platform analyzes historical data to predict resource needs and staffing requirements, enhancing planning and operations.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser3.png" alt="service 3" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Remote Operations</h2>
                            <div class="container"><span>It allows users real-time, remote access to library operations, offering flexibility and uninterrupted service management.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser4.png" alt="service 4" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Seamless System Integration</h2>
                            <div class="container"><span>The goal is to integrate Librainian with existing tools, creating a unified and efficient library and member management system.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser5.png" alt="service 5" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Scalable CRM Solutions</h2>
                            <div class="container"><span>This versatile tool scales effortlessly to meet data and user demands, delivering reliable and robust performance to libraries.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser6.png" alt="service 6" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Customizable CRM Dashboards</h2>
                            <div class="container"><span>Users can customize dashboards for key insights, boosting productivity and allowing personalized views for efficient management.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser7.png" alt="service 7" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Member Relationship Management</h2>
                            <div class="container"><span>Librainian enables efficient member management, personalized notifications, and engagement tracking, enhancing user experience.</span></div>
                    </li>
                    <li class="card">
                        <div class="img">
                            <img src="/static/img/figma_files/ser8.png" alt="service 8" draggable="false">
                        </div>
                            <h2 style="font-weight:bold; text-align: center">Comprehensive Analytics & Reporting</h2>
                            <div class="container"><span>This project provides detailed reports on engagement, resource use, and operations, enabling data-driven decision-making.</span></div>
                    </li>
                </ul>
                <i id="right" class="fa-solid fas fa-angle-right"></i>
            </div>
        </section>

        <section style="background-color: #3b719f; text-align: center; max-width: 1080px; margin: 0 auto" >
            <div class="cyber_desk">
                <img src="/static/img/cyber-security_desk.jpg" alt="Cyber Security" class="cyber_desk img-fluid">

            </div>
            <img src="/static/img/cyber-security_mob.jpg" alt="Cyber Security" class="cyber_mob">

        </section>

        <section id="testimonials" class="container">
            <p class="main-text">Our Testimonials</p>
            <div class="testimonials-section">
                <div class="testimonials-container">
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <img src="/static/img/figma_files/avatar.png" alt="User Icon">
                            <div  class="text-start">
                                <h3>Rajesh Sansanwal</h3>
                                <p>CEO, Apna Library Delhi</p>
                            </div>
                        </div>
                        <p class="testimonial-text">
                            Implementing Librainian has revolutionized how we manage our library resources. The advanced data analytics and accurate forecasting features have enabled us to anticipate user needs and optimize our collection management. It's incredibly user-friendly, and the transition to digital management has been seamless, allowing us to access real-time data from anywhere.
                        </p>
                    </div>


                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <img src="/static/img/figma_files/avatar.png" alt="User Icon">
                            <div class="text-start">
                                <h3>Gayatri Devi</h3>
                                <p>Owner, Gayatri Study Centers, Jaunpur</p>
                            </div>
                        </div>
                        <p class="testimonial-text">
                            Librainian stands out with its powerful data analytics and remote accessibility. Managing our study center has never been easier. We can track inventory, monitor member activities, and predict future resource requirements with precision. Plus, the platform's security measures give us peace of mind that our data is always protected.
                        </p>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <img src="/static/img/figma_files/avatar.png" alt="User Icon">
                            <div class="text-start">
                                <h3>Isfaqulla Khan</h3>
                                <p>Founder, Study Union Delhi</p>
                            </div>
                        </div>
                        <p class="testimonial-text">
                            For a small business like ours, Librainian has been a game-changer. The ability to manage our library digitally and access detailed, accurate data from our mobile devices has streamlined our operations significantly. Their customer support team is also fantastic, always ready to help with any queries.
                        </p>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <img src="/static/img/figma_files/avatar.png" alt="User Icon">
                            <div class="text-start">
                                <h3>Deepak Patel</h3>
                                <p>Patel Library, Mumbai</p>
                            </div>
                        </div>
                        <p class="testimonial-text">
                            Implementing Librainian has revolutionized how we manage our library resources. The advanced data analytics and accurate forecasting features have enabled us to anticipate user needs and optimize our collection management. It's incredibly user-friendly, and the transition to digital management has been seamless, allowing us to access real-time data from anywhere.
                        </p>
                    </div>
                </div>


            </div>
        </section>


        <!-- <section id="team" class="container">
            <p class="main-text">Meet Our Team</p>
                <div class="team-box">
                    <div class="profile-box">
                        <div class="img-cont">
                            <img src="/static/img/figma_files/Uday.png" alt="">
                        </div>
                        <div class="name">Uday Shanker</div>
                        <div class="desig">Managing Director & CEO</div>
                        <div class="socials">
                            <i class="fa-brands fa-2x fa-twitter"></i>
                            <i class="fa-brands fa-2x fa-facebook"></i>
                            <i class="fa-brands fa-2x fa-instagram"></i>
                            <i class="fa-brands fa-2x fa-linkedin"></i>
                        </div>
                    </div>
                    <div class="profile-box">
                        <div class="img-cont">
                            <img src="/static/img/figma_files/Mihir.png" alt="">
                        </div>
                        <div class="name">Mihir Deo</div>
                        <div class="desig">Founder & CTO</div>
                        <div class="socials">
                            <i class="fa-brands fa-2x fa-twitter"></i>
                            <i class="fa-brands fa-2x fa-facebook"></i>
                            <i class="fa-brands fa-2x fa-instagram"></i>
                            <i class="fa-brands fa-2x fa-linkedin"></i>
                        </div>
                    </div>
                    <div class="profile-box">
                        <div class="img-cont">
                            <img src="/static/img/figma_files/MERN.png" alt="Frontend Developer">
                        </div>
                        <div class="name">Nayan Das</div>
                        <div class="desig">Frontend Developer</div>
                        <div class="socials">
                            <i class="fa-brands fa-2x fa-twitter"></i>
                            <i class="fa-brands fa-2x fa-facebook"></i>
                            <i class="fa-brands fa-2x fa-instagram"></i>
                            <i class="fa-brands fa-2x fa-linkedin"></i>
                        </div>
                    </div>
                    <div class="profile-box">
                        <div class="img-cont">
                            <img src="/static/img/figma_files/vipin.png" alt="">
                        </div>
                        <div class="name">Vipin Jarwal</div>
                        <div class="desig">Backend Developer</div>
                        <div class="socials">
                            <i class="fa-brands fa-2x fa-twitter"></i>
                            <i class="fa-brands fa-2x fa-facebook"></i>
                            <i class="fa-brands fa-2x fa-instagram"></i>
                            <i class="fa-brands fa-2x fa-linkedin"></i>
                        </div>
                    </div>
            </div>
        </section> -->


        <section id="pricing">
            <p class="main-text mb-1 pricing_p">Pricings We Provide</p>

            <section id="pricing" class="pricing section-bg">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mt-4 mt-lg-0">
                            <div class="box" data-aos="zoom-in" data-aos-delay="100" style="height: 31rem;">
                                <h3>Standard</h3>
                                <!-- <h4><sup class="strike">₹399</sup>₹199<span> / month</span></h4> -->
                                <h4>₹399<span> / month</span></h4>
                                <ul>
                                    <li>Free Listing on a site</li>
                                    <li></li>
                                    <li>Dashboard</li>
                                    <li></li>
                                    <li>Send promotion message upto 200</li>
                                    <li></li>
                                    <li>Contain Resource Management Tool</li>
                                    <li></li>
                                </ul>
                                <div class="btn-wrap ">
                                    <a href="/librarian/signup/" class="btn-buy ">Buy Now</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mt-4 mt-md-0">
                            <div class="box recommended" data-aos="zoom-in" style="height: 31rem;">
                                <span class="recommended-badge">Recommended</span>
                                <h3>Advanced</h3>
                                <h4>₹499<span> / month</span></h4>
                                <ul>
                                    <li>Everything in Standard </li>
                                    <li>Send promotion message</li>
                                    <li>upto 400</li>
                                    <li>Info Graphics</li>
                                    <li>Daily cashflow</li>
                                    <li>Daily finicial statics</li>
                                </ul>
                                <div class="btn-wrap">
                                    <a href="/librarian/signup/" class="btn-buy">Buy Now</a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 mt-4 mt-lg-0">
                            <div class="box" data-aos="zoom-in" data-aos-delay="100" style="height: 31rem;">
                                <h3>Premium</h3>
                                <h4></sup>₹699<span> / month</span></h4>
                                <ul>
                                    <li>Everything in Advanced </li>
                                    <li>Daily & Monthly financial Flow</li>
                                    <li>Send Promotion message</li>
                                    <li>upto 1000</li>
                                    <li>Info Graphics</li>
                                    <li>Daily Cashflow</li>
                                </ul>
                                <div class="btn-wrap">
                                    <a href="/librarian/signup/" class="btn-buy">Buy Now</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>
        <section id="register" class="register-section py-5" style="background-color: #f8f9fa;">
            <div class="container">
                <p class="main-text">Join Our Community</p>
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="row align-items-center">
                            <!-- Content Section -->
                            <div class="col-md-6">
                                <div class="register-content">
                                    <h2>Become Part of Our Growing Network</h2>
                                    <p class="mb-4">Join the community of 600+ libraries already using Librainian to transform their operations. Get access to powerful tools, analytics, and a network of library professionals.</p>
                                    <ul class="register-features">
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Easy onboarding process</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Free listing on our library search engine</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Access to comprehensive dashboard</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Connect with students in your area</li>
                                    </ul>
                                    <a href="/librarian/signup/" class="btn btn-primary mt-3 register-btn">
                                        <i class="fas fa-user-plus me-2"></i> Register Now
                                    </a>
                                </div>
                            </div>

                            <!-- Image Section -->
                            <div class="col-md-6 text-center">
                                <img src="/static/img/figma_files/library_register.png" class="img-fluid register-image" alt="Library Registration">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="find-libraries" class="library-search-section py-5">
            <div class="container">
                <p class="main-text">Find Libraries Near You</p>
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="row align-items-center">
                            <!-- Content Section -->
                            <div class="col-md-6">
                                <div class="library-search-content">
                                    <h2>Explore Registered Libraries</h2>
                                    <p class="mb-4">Looking for a library or study center in your area? Our search engine helps students find the perfect library that meets their needs. Browse through 600+ registered libraries across the country.</p>
                                    <ul class="library-features">
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Find libraries by location</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> View library facilities and resources</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Check availability and operating hours</li>
                                        <li><i class="fas fa-check-circle text-primary me-2"></i> Connect with library administrators</li>
                                    </ul>
                                    <a href="https://www.librainian.com/librarian/library-list/" class="btn btn-primary mt-3 search-libraries-btn">
                                        <i class="fas fa-search me-2"></i> Search Libraries
                                    </a>
                                </div>
                            </div>

                            <!-- Image Section -->
                            <div class="col-md-6 text-center">
                                <img src="/static/img/figma_files/library_search.svg" class="img-fluid library-search-image" alt="Library Search">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="blog" class="blog-section py-5" style="background-color: #f8f9fa;">
            <div class="container">
                <p class="main-text">Our Blog</p>
                <div class="row justify-content-center mb-4">
                    <div class="col-lg-8 text-center">
                        <h2 class="blog-subtitle">Insights, Updates & Knowledge Hub</h2>
                        <p class="blog-description">Explore our collection of articles about library management, security features, app functionality, and best practices for modern librarians.</p>
                    </div>
                </div>

                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="blog-overview-card">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="blog-overview-content">
                                        <h3>Stay Informed with Librainian</h3>
                                        <p class="mb-4">Our blog features articles on library management best practices, security tips, feature updates, and industry trends to help you get the most out of your library management system.</p>
                                        <ul class="blog-topics">
                                            <li><i class="fas fa-check-circle text-primary me-2"></i> Library management tips & tricks</li>
                                            <li><i class="fas fa-check-circle text-primary me-2"></i> Security and data protection</li>
                                            <li><i class="fas fa-check-circle text-primary me-2"></i> Feature updates and tutorials</li>
                                            <li><i class="fas fa-check-circle text-primary me-2"></i> Industry trends and insights</li>
                                        </ul>
                                        <a href="https://librainian.com/blogs/p/" class="btn btn-primary mt-3 blog-main-btn">
                                            <i class="fas fa-book-open me-2"></i> Visit Our Blog
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 text-center">
                                    <img src="/static/img/figma_files/tips.svg" alt="Librainian Blog" class="img-fluid blog-overview-image">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="faq">
            <p class="main-text">Frequently Asked Questions</p>
            <div class="container faq_section">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseOne" aria-expanded="true" aria-controls="panelsStayOpen-collapseOne">
                                <strong>What is Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body">
                                <el>Librainian is a cutting-edge web app built on Python, renowned for its capabilities in data analytics and data science. It features advanced tools for predicting and analyzing data, offering precise forecasting and insights for all aspects of your library management.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseTwo" aria-expanded="false" aria-controls="panelsStayOpen-collapseTwo">
                                <strong>How does Librainian work ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseTwo" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Librainian utilizes Python to harness powerful data analytics, enabling features like
                                    accurate forecasting and comprehensive data analysis. It's designed to help libraries efficiently manage and predict various aspects of their operations, ensuring you have the information you need to make informed decisions.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseThree" aria-expanded="false" aria-controls="panelsStayOpen-collapseThree">
                                <strong>What are the benefits of using Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseThree" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Librainian transforms your library management by going digital, providing accurate analytics, and  offering better overall management. You have full control over your library's operations via both your phone and laptop, making it easy to stay informed and efficient.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseFour" aria-expanded="false" aria-controls="panelsStayOpen-collapseFour">
                                <strong>Is Librainian suitable for my library or study center ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseFour" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Librainian is perfect for any library or study center looking to maintain accurate,
                                    real-time details
                                    even when the administrator isn't on-site. It ensures you receive real, unmanipulated
                                    data, making it
                                    an invaluable tool for remote management</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseFive" aria-expanded="false" aria-controls="panelsStayOpen-collapseFive">
                                <strong>How secure is Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseFive" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>We prioritize user data and privacy with the utmost seriousness. Librainian employs
                                    robust
                                    security measures to protect your information, and we never sell your data to third
                                    parties. Our
                                    security protocols are designed to meet all essential requirements to keep your data
                                    safe.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseSix" aria-expanded="false" aria-controls="panelsStayOpen-collapseSix">
                                <strong>What support options are available with Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseSix" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Our customer support is easily accessible. You can reach us via email or call us at
                                    620-762-8282
                                    for assistance. We are here to help with any issues or questions you might have.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseSeven" aria-expanded="false" aria-controls="panelsStayOpen-collapseSeven">
                                <strong>What are the pricing plans for Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseSeven" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>
                                    We offer three pricing plans:
                                    <ul>
                                        <!-- <li>A free plan at ₹0/month.</li> -->
                                        <li>A standard plan originally costs ₹399/month; coupons can be applied.</li>
                                        <li>A Advanced plan originally costs ₹499/month; coupons can be applied.</li>
                                        <li>A Premium plan originally costs ₹699/month; coupons can be applied.</li>
                                    </ul>
                                    <el>Detailed pricing can be found on our pricing page.</el>
                                </el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseEight" aria-expanded="false" aria-controls="panelsStayOpen-collapseEight">
                                <strong>How do I get started with Librainian ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseEight" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Getting started with Librainian is easy! Simply sign up on our website by providing
                                    your basic
                                    details. After registration, you'll go through a quick onboarding process that will
                                    guide you through
                                    setting up and using the system effectively.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseNine" aria-expanded="false" aria-controls="panelsStayOpen-collapseNine">
                                <strong>Can I access Librainian on mobile devices ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseNine" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>Yes, Librainian is designed to be fully accessible on any device. It uses
                                    mobile-responsive web
                                    interfaces, ensuring you can manage your library seamlessly from your smartphone,
                                    tablet, or
                                    computer.</el>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#panelsStayOpen-collapseTen" aria-expanded="false" aria-controls="panelsStayOpen-collapseTen">
                                <strong>How often is Librainian updated ?</strong>
                            </button>
                        </h2>
                        <div id="panelsStayOpen-collapseTen" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <el>We are committed to continuous improvement. Librainian is regularly updated to
                                    introduce new
                                    features, enhance performance, and ensure the highest levels of security and
                                    functionality.</el>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="download">
            <p class="alter-text">Download Our App</p>
            <div class="container down-cont">
                <div class="down-sec">
                    <div class="play-1">
                        <p class="main-text mx-3">Download Our App</p>
                        <p class="down-text mx-3">
                            Download the app to securely store and manage your information. Fast, secure, and easy to use, it simplifies daily tasks. Experience convenience—download now!
                        </p>
                        <div class="play-store">
                            <a href="https://play.google.com/store/apps/details?id=com.librainian.twa">
                               <img src="/static/img/figma_files/play_store_.png" alt="">
                            </a>
                        </div>
                    </div>
                    <div class="play-2 image-container">
                        <img src="/static/img/figma_files/mobile_.png" class="down_img" alt="">
                        <div class="hotspot" onclick="handleClick()">+</div>
                    </div>
                </div>
            </div>
        </section>



        <section id="contact">
            <p class="main-text">Contact Us</p>
            <section class="sec8">
                <div class="add sec8-2">
                    <h1 class="head">Address Information</h1>
                    <p class="desc">F2/9 Jai Durga Society Netaji Nagar Hill no.3, 90ft road Sakinaka, Kurla West Mumbai 400072</p>

                    <div class="add1">
                        <h2>Email Us</h2>
                            <p class="des"><EMAIL></p>
                    </div>
                    <div class="add2">
                        <h2>Contact Us</h2>
                            <p class="des">+91 6207628282</p>
                    </div>
                </div>
                <div class="touch sec8-2">
                    <h1 class="head get_in">Get In Touch</h1>
                    <input type="text" placeholder="Name">
                    <input type="email" placeholder="E-Mail">
                    <input type="text" placeholder="Subject">
                    <textarea name="" id="" cols="20" rows="1" placeholder="Message"></textarea>
                    <button class="submit-btn">Submit Now</button>
                </div>
            </section>
        </section>


        <div id="cookieConsent" class="cookie-consent">
            <p>We use cookies to improve your experience on our site. By accepting, you agree to our use of cookies.</p>
            <button id="acceptCookies">Accept</button>
        </div>
        <button id="scrollUpBtn" onclick="scrollToTop()"><i class="fa-4x fa-regular fa-circle-up"></i></button>
    </static/img>

    <footer id="footer">
        <div class="footer-top">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <a href="#header" class="scrollto footer-logo"><img src="/static/img/figma_files/logo 1.png" alt=""></a>
                        <h3>Librarian</h3>
                    </div>
                </div>

                <div class="row footer-newsletter justify-content-center">
                    <div class="col-lg-6">
                        <form action="" method="post">
                            <input type="email" name="email" placeholder="Enter your Email"><input type="submit" value="Subscribe">
                        </form>
                    </div>
                </div>

                <div class="social-links">
                    <a href="https://www.facebook.com/people/Librainian/61562707884730/" class="facebook"><i class="fa-brands fa-facebook"></i></a>
                    <a href="https://www.instagram.com/librainian.app/" class="instagram"><i class="fa-brands fa-instagram"></i></a>
                    <a href="https://www.linkedin.com/company/pinak-venture/" class="linkedin"><i class="fa-brands fa-linkedin"></i></a>
                    <a href="https://www.youtube.com/channel/UCPf3m0ZR8XN7DVMOM6OJ8Qw" class="youtube"><i class="fa-brands fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="container footer-bottom clearfix">
              <!-- Footer Links Section (Multi-Column Layout) -->
          <div class="footer-links" class="container footer-bottom clearfix" >
            <div class="row justify-content-center">
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <ul style="list-style: none; padding: 0; text-align: center;">
                        <li style="padding: 8px 0;"><a href="/blogs/p/terms-of-use" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Terms of Use</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/about" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">About</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/privacy-policy/" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Privacy Policy</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/contact" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <ul style="list-style: none; padding: 0; text-align: center;">
                        <li style="padding: 8px 0;"><a href="/blogs/p/security" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Security</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/cancellation-return" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Cancellation & Return</a></li>
                        {% comment %} <li><a href="/blogs/p/form" style="color: #fff; font-weight: bold; text-decoration: none;">Form</a></li> {% endcomment %}
                        <li style="padding: 8px 0;"><a href="/blogs/p/affordable" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Affordable</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <ul style="list-style: none; padding: 0; text-align: center;">
                        <li style="padding: 8px 0;"><a href="/blogs/p/student-verification" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Student Verification</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/digital-fee" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Digital Fee</a></li>
                        <li style="padding: 8px 0;"><a href="/blogs/p/services" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Services</a></li>
                        <li style="padding: 8px 0;"><a href="http://pinakventure.com/team" style="color: #fff; font-weight: bold; text-decoration: none; font-size: 18px;">Out Team</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="copyright ">
                &copy; Copyright <strong><span>Pinak Venture</span></strong>. All Rights Reserved
            <div class="links"> <a href="/sitemap.xml" class="text-white text-decoration-none">Sitemap</a> | <a href="/robots.txt" class="text-white text-decoration-none">Robots.txt</a>
            </div>
            </div>
        </div>
    </footer>


    <!-- Cookie Consent Script -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const cookieBanner = document.getElementById("cookieConsent");
            const acceptButton = document.getElementById("acceptCookies");

            function setCookie(name, value, days) {
                const date = new Date();
                date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
                const expires = "expires=" + date.toUTCString();
                document.cookie = `${name}=${value}; ${expires}; path=/`;
            }

            function getCookie(name) {
                const cookieArr = document.cookie.split("; ");
                for (let i = 0; i < cookieArr.length; i++) {
                    const cookiePair = cookieArr[i].split("=");
                    if (name === cookiePair[0]) {
                        return cookiePair[1];
                    }
                }
                return null;
            }

            if (!getCookie("cookieConsentAccepted")) {
                cookieBanner.style.display = "block";
            }

            acceptButton.addEventListener("click", function () {
                setCookie("cookieConsentAccepted", "true", 30);
                cookieBanner.style.display = "none";
            });
        });
    </script>

    <script>
        window.onscroll = function() { toggleScrollButton() };

        function toggleScrollButton() {
            const scrollUpBtn = document.getElementById("scrollUpBtn");
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                scrollUpBtn.style.display = "block";
            } else {
                scrollUpBtn.style.display = "none";
            }
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", () => {
    const sections = document.querySelectorAll("main > div");

    const options = {
        root: null, // viewport
        threshold: 0.1, // trigger when 10% of the section is visible
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add("section");
                observer.unobserve(entry.target); // stop observing once animated
            }
        });
    }, options);

    sections.forEach(section => observer.observe(section));
});

    </script>


    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const carousel = document.querySelector(".carousel");
            const arrowBtns = document.querySelectorAll(".wrapper i");
            const wrapper = document.querySelector(".wrapper");

            const firstCard = carousel.querySelector(".card");
            const firstCardWidth = firstCard.offsetWidth;

            let isDragging = false,
                startX,
                startScrollLeft,
                timeoutId;

            const dragStart = (e) => {
                isDragging = true;
                carousel.classList.add("dragging");
                startX = e.pageX;
                startScrollLeft = carousel.scrollLeft;
            };

            const dragging = (e) => {
                if (!isDragging) return;

                // Calculate the new scroll position
                const newScrollLeft = startScrollLeft - (e.pageX - startX);

                // Check if the new scroll position exceeds
                // the carousel boundaries
                if (newScrollLeft <= 0 || newScrollLeft >=
                    carousel.scrollWidth - carousel.offsetWidth) {

                    // If so, prevent further dragging
                    isDragging = false;
                    return;
                }

                // Otherwise, update the scroll position of the carousel
                carousel.scrollLeft = newScrollLeft;
            };

            const dragStop = () => {
                isDragging = false;
                carousel.classList.remove("dragging");
            };

            const autoPlay = () => {

                // Return if window is smaller than 800
                if (window.innerWidth < 800) return;

                // Calculate the total width of all cards
                const totalCardWidth = carousel.scrollWidth;

                // Calculate the maximum scroll position
                const maxScrollLeft = totalCardWidth - carousel.offsetWidth;

                // If the carousel is at the end, stop autoplay
                if (carousel.scrollLeft >= maxScrollLeft) return;

                // Autoplay the carousel after every 2500ms
                timeoutId = setTimeout(() =>
                    carousel.scrollLeft += firstCardWidth, 2500);
            };

            carousel.addEventListener("mousedown", dragStart);
            carousel.addEventListener("mousemove", dragging);
            document.addEventListener("mouseup", dragStop);
            wrapper.addEventListener("mouseenter", () =>
                clearTimeout(timeoutId));
            wrapper.addEventListener("mouseleave", autoPlay);

            // Add event listeners for the arrow buttons to
            // scroll the carousel left and right
            arrowBtns.forEach(btn => {
                btn.addEventListener("click", () => {
                    carousel.scrollLeft += btn.id === "left" ?
                        -firstCardWidth : firstCardWidth;
                });
            });
        });
    </script>

    <script>
        function handleClick() {
            window.location.href = "/download-app/";
        }
    </script>

<script>
    function closeMenu() {
        // Get the navbar collapse element
        const navbarCollapse = document.getElementById('navbarSupportedContent');
        // Create a Bootstrap collapse instance
        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {toggle: false});
        // Hide the navbar
        bsCollapse.hide();
    }
</script>

<script>
    function incrementNumber(elementId, min, max) {
      const targetNumber = Math.floor(Math.random() * (max - min + 1)) + min;
      const incrementSpeed = 50; // Milliseconds
      let currentNumber = 0;

      const interval = setInterval(() => {
        if (currentNumber >= targetNumber) {
          clearInterval(interval);
        } else {
          currentNumber += Math.ceil((targetNumber - currentNumber) / 10);
          document.getElementById(elementId).innerText = currentNumber;
        }
      }, incrementSpeed);
    }

    // Trigger the animations on page load
    window.onload = () => {
      incrementNumber("invoices", 2100, 2400);
      incrementNumber("students", 800, 1200);
    };
  </script>


    <!-- Bootstrap Bundle with Popper (includes all Bootstrap JS) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Bootstrap initialization check -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Bootstrap version:', bootstrap.Collapse.VERSION);

            // Check if navbar toggler exists
            const navbarToggler = document.querySelector('.navbar-toggler');
            if (navbarToggler) {
                console.log('Navbar toggler found:', navbarToggler);

                // Add a click event listener to log when it's clicked
                navbarToggler.addEventListener('click', function() {
                    console.log('Navbar toggler clicked');
                });
            } else {
                console.error('Navbar toggler not found');
            }
        });
    </script>

    <!-- <script>
        window.addEventListener("load", function () {
          const pageUrl = window.location.pathname; // Get current page URL
          const storageKey = "page_loads";
          const lastApiCallKey = "lastApiCallTimestamp";
          const intervalTime = 10000*6; // 10 seconds

          // Get existing pageCounts from localStorage or initialize a new object
          let pageCounts = JSON.parse(localStorage.getItem(storageKey)) || {};

          // Increment the count for the current page
          pageCounts[pageUrl] = (pageCounts[pageUrl] || 0) + 1;

          // Save updated counts back to localStorage
          localStorage.setItem(storageKey, JSON.stringify(pageCounts));

          // Function to send data to the API
          function sendPageCountsToAPI() {
            var origin = location.origin;
            const storedData = JSON.parse(localStorage.getItem(storageKey));

            if (storedData && Object.keys(storedData).length > 0) {
                console.log(origin+"/track-page-view/");
              fetch(origin+"/librarian/track-page-view/", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-CSRFToken": "{{ csrf_token }}",
                },
                body: JSON.stringify(storedData),
              })
                .then((response) => response.json())
                .then((data) => {
                  console.log("Data sent successfully:", data);
                  localStorage.removeItem(storageKey); // Clear after sending
                  localStorage.setItem(lastApiCallKey, Date.now()); // Update last API call time
                })
                .catch((error) => {
                  console.error("Error sending data:", error);
                  localStorage.setItem(lastApiCallKey, Date.now()); // Update last API call time
            });
            }
          }

          // Check when was the last API call
          const lastApiCall = localStorage.getItem(lastApiCallKey);
          const now = Date.now();
          console.log(!lastApiCall || now - lastApiCall >= intervalTime);


          if (!lastApiCall || now - lastApiCall >= intervalTime) {
            sendPageCountsToAPI(); // Make API request immediately if 10s have passed
          }

          // Set an interval that persists even after page reloads
          setInterval(() => {
            const lastApiCall = localStorage.getItem(lastApiCallKey);
            console.log(lastApiCall);

            const now = Date.now();
            console.log(now);

            console.log(!lastApiCall || now - lastApiCall >= intervalTime);


            if (!lastApiCall || now - lastApiCall >= intervalTime) {
              sendPageCountsToAPI(); // Send request every 10s
            }
          }, 10000); // Check every second if 10s have passed
        });
      </script> -->
      <script>
        window.addEventListener("load", function () {
            const path = window.location.pathname; // Get current page path
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

            // Increment count for the current path
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

            // Store updated data back to localStorage
            localStorage.setItem("page_data", JSON.stringify(pageData));
          });

          // Function to send page view data
          function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

            if (Object.keys(pageData).length > 0) {
              fetch(location.origin + "/librarian/track-page-view/", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-CSRFToken": "{{ csrf_token }}",
                },
                body: JSON.stringify(pageData),
              })

                .then(() => {
                  localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
                localStorage.removeItem("page_data");
            } else {

              console.log("No page data to send");
            }
          }

          // Send data every 10 seconds
          setInterval(sendPageData, 10000);
    </script>
    </body>
</html>
