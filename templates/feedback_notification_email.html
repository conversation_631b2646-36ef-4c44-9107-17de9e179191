<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Feedback Received</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .header {
            background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 24px;
            letter-spacing: 0.5px;
        }

        .header-icon {
            margin-bottom: 15px;
            font-size: 32px;
        }

        .content {
            padding: 30px;
        }

        .intro {
            text-align: center;
            margin-bottom: 25px;
            color: #555;
            font-size: 16px;
        }

        .feedback-item {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eaedf2;
        }

        .feedback-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #4776E6;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .value {
            margin-bottom: 0;
            font-size: 16px;
            color: #333;
            line-height: 1.5;
        }

        .rating {
            color: #FFD700;
            font-size: 24px;
            letter-spacing: 2px;
        }

        .message-box {
            background-color: #f8f9fc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4776E6;
        }

        .meta-info {
            background-color: #f8f9fc;
            padding: 15px;
            border-radius: 8px;
            margin-top: 25px;
        }

        .meta-info .feedback-item {
            margin-bottom: 10px;
            padding-bottom: 10px;
        }

        .footer {
            background-color: #f8f9fc;
            padding: 20px;
            text-align: center;
            font-size: 13px;
            color: #777;
            border-top: 1px solid #eaedf2;
        }

        .footer p {
            margin: 0;
        }

        .logo {
            margin-top: 10px;
            opacity: 0.8;
        }

        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-icon">📝</div>
            <h2>New Feedback Received</h2>
        </div>

        <div class="content">
            <p class="intro">A new feedback has been submitted through the registration success page.</p>

            <div class="feedback-item">
                <div class="label">Name</div>
                <div class="value">{{ feedback.name }}</div>
            </div>

            <div class="feedback-item">
                <div class="label">Email</div>
                <div class="value">{{ feedback.email }}</div>
            </div>

            <div class="feedback-item">
                <div class="label">Phone</div>
                <div class="value">{{ feedback.phone }}</div>
            </div>

            <div class="feedback-item">
                <div class="label">Subject</div>
                <div class="value">{{ feedback.subject }}</div>
            </div>

            <div class="feedback-item">
                <div class="label">Rating</div>
                <div class="value rating">
                    {% for i in feedback.get_rating_display_stars %}{{ i }}{% endfor %}
                </div>
            </div>

            <div class="feedback-item">
                <div class="label">Message</div>
                <div class="value message-box" style="white-space: pre-line;">{{ feedback.message }}</div>
            </div>

            {% if feedback.library %}
            <div class="feedback-item">
                <div class="label">Library</div>
                <div class="value">{{ feedback.library.library_name }}</div>
            </div>
            {% endif %}

            <div class="meta-info">
                <div class="feedback-item">
                    <div class="label">Submitted On</div>
                    <div class="value">{{ feedback.created_at|date:"F j, Y, g:i a" }}</div>
                </div>

                <div class="feedback-item">
                    <div class="label">IP Address</div>
                    <div class="value">{{ feedback.ip_address }}</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>This is an automated message from the LIBRAINIAN.</p>
            <div class="logo"><img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);"></div>
        </div>
    </div>
</body>
</html>
