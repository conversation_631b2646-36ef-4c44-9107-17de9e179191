body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
    background-color: #f5f0f0;
}



.dashboard-thead {
  padding: 1rem; 
  border-radius: 1rem !important;
}



.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.profile-img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    /* margin-bottom: 15px; */
    border-radius: 50%;
    margin-left: 10px;
}

/* .form-control {
    border: none;
    outline: none;
    box-shadow: none;
    margin-left: 18px;
} */

.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

.search-wrapper {
    position: relative;
}

.search-wrapper input[type="search"] {
    padding-left: 2.5rem;
}

.search-wrapper .fa-magnifying-glass {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0b0b0b;
}

.card {
    margin-bottom: 20px;
    border: none;
    border-radius: 1rem !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 1rem !important;
}

.card .card-body {
    position: relative;
    padding: 20px;
}

.card .card-body .icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.15);
}

.card .card-footer {
    background-color: transparent;
    border-top: none;
}

@media only screen and (max-width: 767px) {



#basic-datatables tr {
    padding: 1rem;
    border-radius: 1rem;
    background-color: #e7fbfb;
}

#basic-datatables table {
    border-radius: 1rem;
}

#basic-datatables2 tr {
    padding: 1rem;
    border-radius: 1rem;
    background-color: #e7fbfb;
}

#basic-datatables3 tr {
    padding: 1rem;
    border-radius: 1rem;
    background-color: #e7fbfb;
}

    .small_p p{
    font-size: 12px !important;
}
}
/* Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    background-color: #f1f8ff;
    padding: 10px 0;
    z-index: 1000;
}

.footer-menu a,
.footer-menu .settings-link {
    color: #000000;
    font-size: 24px;
    text-align: center;
    text-decoration: none;
    position: relative;
}

.footer-menu a.active i,
.footer-menu .settings-link.active i {
    color: #020202;
}

.footer-menu .settings-link .submenu {
    display: none;
    position: absolute;
    bottom: 65px;
    left: -250px;
    background-color: #ffffff;
    border: 1px solid #000000;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
    padding: 10px;
    z-index: 1001;
    white-space: nowrap;
}

.footer-menu .settings-link a {
    display: block;
    color: #000000;
    padding: 5px 10px;
    text-decoration: none;
    text-align: start;
    margin-top: 10px;
}

.footer-menu .settings-link a:hover {
    background-color: #000000;
    color: white;
    border-radius: 3px;
}

.footer-menu .settings-link.active {
    display: block;
}

.submenu {
    display: none;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 0 10px rgb(192, 221, 253);
    z-index: 1000;
    left: 0;
    margin-top: 5px;
    padding: 10px;
    height: auto;
    width: 310px;
}

.sub-submenu {
    display: none;
    position: absolute;
    left: 100%;
    /* Position sub-submenu to the right of submenu */
    top: 0;
    margin-top: -10px;
    /* Adjust top margin as needed */
    padding: 5px 0;
    /* Add padding to sub-submenu */
    background-color: #f9f9f9;
    /* Adjust background color */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
    /* Optional: Add shadow for better visibility */
}

.settings-link:hover .submenu,
.settings-link:focus .submenu {
    display: block;
}

.sub-submenu {
    display: none;
}

.submenu-item:hover .sub-submenu,
.submenu-item:focus .sub-submenu {
    display: block;
}

.btn-success {
    background-color: white;
    color: #000000;
}

.btn-success:hover {
    background-color: green;
    color: white;
}

.btn-danger {
    background-color: white;
    color: rgb(0, 0, 0);
}

.btn-danger:hover {
    background-color: rgb(230, 17, 17);
    color: rgb(255, 255, 255);
}

.btn-link {
    color: #000000;
    padding-left: 2px;
    margin-bottom: -12px;
}

.btn-link:hover {
    color: #000000;
    text-decoration: none;
}

.submenu a {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #333;
    /* Example text color */
}

.submenu a:hover {
    background-color: #f0f0f0;
    /* Example hover background color */
}

.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    margin: 0 auto;
    font-size: 14px;
    text-align: center;
}

.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;

}

/* 
.footer p {
    font-size: 12px;
    padding-top: 5px;
} */

@media (max-width:768px) {
    .footer {
        margin-bottom: 50px;
    }

    .footer img {
        width: 60%;
        padding-bottom: 60px;
    }

}

#notificationBtn {
    display: none;
}

@media screen and (max-width: 767px) {

    .table-responsive table,
    .table-responsive thead,
    .table-responsive tbody,
    .table-responsive th,
    .table-responsive td,
    .table-responsive tr {
        display: block;
    }

    
    td[data-label="Email"] {
            word-wrap: break-word; /* Allow word wrapping */
            word-break: break-all;  /* Break long words if needed */
            white-space: normal;    /* Ensure text can wrap within the cell */
        }

    .table-responsive thead tr {
       display: grid;
       grid-template-columns: 40% 60%;
    }

    .table-responsive tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
    }


    .table-responsive td {
        border: none;
        border-bottom: 1px solid #eee;
        position: relative;
        padding-left: 50%;
        white-space: normal;
        text-align: left;
    }

    .table-responsive td:before {
        position: absolute;
        top: 6px;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: bold;
        content: attr(data-label);
    }
}

.card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
    border-radius: 1rem;
}

.card:hover {
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 1rem;
}

.card-title {
    margin-bottom: 0;
    color: #333;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, .05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, .075);
}

.thead-dark th {
    color: #fff;
    background-color: #343a40;
    border-color: #454d55;
}

.icon-text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-text-wrapper i {
    font-size: 24px;
    /* Adjust the icon size as needed */
    margin-bottom: 5px;
    /* Space between icon and text */
}

.dashboard-text {
    font-size: 12px;
    /* Match the font size to the icon size */
    line-height: 24px;
    /* Set the line-height to match the icon's height */
}

@media only screen and (min-width: 770px) {
    .table-responsive thead {
       background-color: #343a40;
       color: #fff;
    }

    body {
        background: #cee3e0 !important;
    }
}

