<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">
    <title>Unauthorized Access | Librainian</title>

    <!-- SEO Meta Tags -->
    <meta name="robots" content="noindex, follow"> <!-- Don't index error pages -->
    <meta name="description" content="Unauthorized access page for Librainian - The #1 Library Management System. Please log in to access this page.">
    <meta name="keywords" content="librainian, library management system, lms, unauthorized access, login required, library software, library management tool">
    <link rel="canonical" href="https://www.librainian.com/unauthorized">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Unauthorized Access | Librainian">
    <meta property="og:description" content="You are not authorized to access this page. Please log in to continue using our library management system.">
    <meta property="og:url" content="https://www.librainian.com/unauthorized">
    <meta property="og:image" content="https://www.librainian.com/static/img/unauthorized.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:title" content="Unauthorized Access | Librainian">
    <meta name="twitter:description" content="You are not authorized to access this page. Please log in to continue using our library management system.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/unauthorized.jpg">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-07-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Unauthorized Access | Librainian",
        "description": "You are not authorized to access this page. Please log in to continue using our library management system.",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Unauthorized Access",
                    "item": "https://www.librainian.com/unauthorized"
                }
            ]
        }
    }
    </script>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <!-- FontAwesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Custom styles -->

    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            background: linear-gradient(135deg, #74b9ff, #a29bfe);
            font-family: 'Montserrat', sans-serif;
            color: #2d3436;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-body {
            padding: 3rem;
        }

        .btn-primary {
            background-color: #0984e3;
            border-color: #0984e3;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            border-radius: 25px;
            transition: background-color 0.3s ease, transform 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #74b9ff;
            transform: scale(1.05);
        }

        .fa-lock {
            font-size: 4rem;
            color: #d63031;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>

    <div class="container">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <i class="fas fa-lock"></i>
                    <h3 class="mb-4">Unauthorized Access</h3>
                    <p class="mb-4">Sorry, you are not authorized to access this page.</p>
                    <a href="/{{role}}/login/" class="btn btn-primary btn-lg">Go to Login</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap and necessary plugins -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>

</html>