<DOCTYPE html>
<html lang="en">
<head>
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     
    <title>{% if blog %}Edit Blog{% else %}Create Blog{% endif %}</title>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">
    

    
    <!-- Disable Right click -->

        
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.3/tinymce.min.js"></script>

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #9bc6bf;
        }

        .card-img-top {
            max-height: 10rem;
        }

        .card-text {
            font-size: 1rem;
        }

        #searchInput {
            max-width: 15rem;
        }

        .form-control{
            border-radius: 1rem;
        }

    </style>
</head>
<body>


    <!-- Sidebar -->
    {% include "sidebar.html" %}

    <!-- Dashboard content -->
    <div class="container d-flex mt-5 d-flex bg-white p-4" style="border-radius: 1rem;">
        <div class="card p-3">
            <h5 class="text-center text-white p-3" style="background-color: #294282; border-radius: 1rem;">{% if blog %}<i class="fas fa-edit icon"></i>Edit Blog{% else %}<i class="fas fa-plus icon"></i>Create Blog{% endif %}</h5>
            <form method="post" enctype="multipart/form-data" class="mt-4">
                {% csrf_token %}
                <div class="form-group">
                    <label for="title"><i class="fas fa-heading icon"></i>Title</label>
                    <input type="text" class="form-control" id="title" name="title" placeholder="Title (50 to 60 characters)" value="{% if blog %}{{ blog.title }}{% endif %}" required>
                </div>
                <div class="form-group">
                    <label for="category"><i class="fas fa-tags icon"></i>Category</label>
                    <input type="text" class="form-control" id="category" name="category" placeholder="Category" value="{% if blog %}{{ blog.category }}{% endif %}" required>
                </div>
                <div class="form-group">
                    <label for="keywords"><i class="fas fa-key icon"></i>Keywords</label>
                    <input type="text" class="form-control" id="keywords" name="keywords" placeholder="Keywords (10 to 15 words)" value="{% if blog %}{{ blog.keyword }}{% endif %}" required>
                </div>
                <div class="form-group">
                    <label for="description"><i class="fas fa-info-circle icon"></i>Description</label>
                    <textarea class="form-control" id="description" name="introduction" placeholder="Description (150 to 160 characters)" rows="3" required>{% if blog %}{{ blog.description }}{% endif %}</textarea>
                </div>
                <div class="form-group">
                    <label for="content"><i class="fas fa-file-alt icon"></i>Content</label>
                    <textarea class="form-control" id="content" name="content" placeholder="Content (600 characters)" rows="6" required>{% if blog %}{{ blog.content }}{% endif %}</textarea>
                </div>
                <div class="form-group">
                    <label for="image"><i class="fas fa-image icon"></i>Image</label>
                    <input type="file" class="form-control-file" id="image" name="image" required>
                </div>
                <div class="form-group text-center">
                    <button type="submit" class="btn text-white" style="background-color: #294282; border-radius: 1rem;">
                        {% if blog %}
                            <i class="fas fa-save icon"></i> Update Blog
                        {% else %}
                            <i class="fas fa-plus icon"></i> Create Blog
                        {% endif %}
                    </button>
                    <a href="/blogs/" class="btn btn-secondary mt-3" style="border-radius: 1rem;"><i class="fas fa-arrow-left icon"></i>Back to Blog List</a>
                </div>
            </form>
        </div>
        <!-- Blog List Section -->
<div class="list-section">
    <hr>
    <div class="d-flex mb-3">
        <div class="d-flex align-items-center">
            <select id="entriesPerPage" class="form-select me-2 py-2 mx-1">
                <option value="3">4</option>
                <option value="6">6</option>
                <option value="24">24</option>
                <option value="48">48</option>
            </select>
        </div>
        <input type="text" class="form-control" id="searchInput" placeholder="Search Blogs...">
    </div>
    <div id="blogList" class="row">
        {% for blog in blogs %}
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <img src="{{ blog.image.url }}" class="card-img-top img-fluid" alt="{{ blog.title }}">
                <div class="card-body">
                    <h5 class="card-title">{{ blog.title }}</h5>
                    <p class="card-text"><strong>Category:</strong> {{ blog.category }}</p>
                    <p class="card-text"><strong>Description:</strong> {{ blog.description|truncatechars:100 }}</p>
                </div>
                <div class="card-footer text-right">
                    <a href="/blogs/{{ blog.id }}" class="btn text-white" style="background-color: #294282; border-radius: 1rem;">View Blog</a>
                </div>
            </div>
        </div>
        {% empty %}
        <p class="text-center">No blogs created yet.</p>
        {% endfor %}
    </div>
    <nav aria-label="Blog pagination">
        <ul id="pagination" class="pagination justify-content-center mt-3">
        </ul>
    </nav>
</div>
</div>

    </div>
    

    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <!-- pagination -->
    <script>
        const blogList = document.getElementById('blogList');
        const searchInput = document.getElementById('searchInput');
        const entriesPerPage = document.getElementById('entriesPerPage');
        const paginationContainer = document.getElementById('pagination');
        
        let currentPage = 1;
        let allBlogs = [...blogList.children];
        let filteredBlogs = allBlogs;
        
        function updatePagination() {
            const pageCount = Math.ceil(filteredBlogs.length / parseInt(entriesPerPage.value));
            paginationContainer.innerHTML = '';
            
            for (let i = 1; i <= pageCount; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                paginationContainer.appendChild(li);
            }
        }
        
        function changePage(page) {
            currentPage = page;
            displayBlogs();
            updatePagination();
        }
        
        function displayBlogs() {
            const start = (currentPage - 1) * parseInt(entriesPerPage.value);
            const end = start + parseInt(entriesPerPage.value);
            const visibleBlogs = filteredBlogs.slice(start, end);
        
            blogList.innerHTML = '';
            if (visibleBlogs.length === 0) {
                blogList.innerHTML = '<p>No matching blogs found.</p>';
            } else {
                visibleBlogs.forEach(blog => blogList.appendChild(blog.cloneNode(true)));
            }
        }
        
        function filterBlogs() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            if (searchTerm === '') {
                filteredBlogs = allBlogs;
            } else {
                filteredBlogs = allBlogs.filter(blog => 
                    blog.textContent.toLowerCase().includes(searchTerm)
                );
            }
            currentPage = 1;
            displayBlogs();
            updatePagination();
        }
        
        searchInput.addEventListener('input', filterBlogs);
        entriesPerPage.addEventListener('change', () => {
            currentPage = 1;
            displayBlogs();
            updatePagination();
        });
        
        // Initial setup
        filterBlogs();
        updatePagination();
        </script>
    <script>
        window.addEventListener("load", function () {
            const path = window.location.pathname; // Get current page path
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object
          
            // Increment count for the current path
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;
          
            // Store updated data back to localStorage
            localStorage.setItem("page_data", JSON.stringify(pageData));
          });
          
          // Function to send page view data
          function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};
          
            if (Object.keys(pageData).length > 0) {
              fetch(location.origin + "/librarian/track-page-view/", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-CSRFToken": "{{ csrf_token }}",
                },
                body: JSON.stringify(pageData),
              })
               
                .then(() => {
                  localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
                localStorage.removeItem("page_data");
            } else {
                
              console.log("No page data to send");
            }
          }
          
          // Send data every 10 seconds
          setInterval(sendPageData, 10000);
    </script>
</body>
</html>