<!DOCTYPE html>
<html lang="en" translate="no">

<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table</title>
     
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    {% load static %}
    <link rel="stylesheet" href="/static/css/table.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">



<body>

    <!-- Sidebar -->
    {% include "sidebar.html" %}
    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center"  style="width: 20rem;">
           DASHBOARD / TABLE
        </div>
    </div>
    <!-- Dashboard content -->
    <div class="container-fluid">


        {% if messages %}
            <div id="messageContainer">
                {% for message in messages %}
                {% if message.tags == 'success' %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {% elif message.tags == 'error' %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {% elif message.tags == 'warning' %}
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            {% elif message.tags == 'info' %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {% else %}
                                <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                    {% endif %}
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

        <div class="row">
            <!-- Student data -->

            {% if role == "librarian" or role == "sublibrarian" %}
            <div class="col-md-12">
                <div class="card p-3">
                    <div class="card-header mb-3">
                        <h4 class="card-title">Student List</h4>
                    </div>
                    <div>
                        <div class="table-responsive">
                            <table id="basic-datatables" class="display rounded-1 table table-striped table-hover">
                                <thead class="dashboard-thead rounded-5">
                                    <tr>
                                        <th>Name</th>
                                        <th>Phone</th>
                                        <th>Email</th>
                                        <th>Course Name</th>
                                        <th>Registration ID</th>
                                        <th>Registration Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for data in students %}
                                    <tr onclick="window.location.href='/students/{{ data.slug }}/'">
                                        <td data-label="Name">{{data.name}}</td>
                                        <td data-label="Phone">{{ data.mobile }} <a href="tel:{{ data.mobile }}" style="margin-left: 8px;"><i class="fas fa-phone-alt" title="Call"></i></a>
                                          </td>
                                        <td data-label="Email">{{data.email}}</td>
                                        <td data-label="Course Name">{{data.course}}</td>

                                        <td data-label="Reg. ID"  >
                                                <a href="/students/{{ data.slug }}/">{{data.unique_id}}</a>
                                        </td>
                                        <td data-label="Reg. Date">{{data.registration_date}}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </div>
                            </a>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration -->

            {% if role != "sublibrarian"   %}
            <div class="col-md-12 mt-3">
            </div>

            <!-- Invoice -->

            <div class="col-md-12 mt-3">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Invoice</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="basic-datatables3" class="display table table-striped table-hover">
                                <thead class="dashboard-thead">
                                    <tr>
                                        <th>S.no</th>
                                        <th>Student Name</th>
                                        <th>Shift</th>
                                        <th>Month</th>
                                        <th>Payment Fee</th>
                                        <th>Payment Date</th>
                                        <th>Due Date</th>
                                        <th>Payment Status</th>
                                        <th>Invoice ID</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    {% for student_id, data in invoice_data.items %}
                                    <tr onclick="window.location.href='/students/invoice_student/{{ data.invoice.slug }}'">
                                        <td data-label="S.no">{{ forloop.counter }}</td>
                                        <td data-label="Std. Name">{{ data.invoice.student.name}}</td>
                                        <td data-label="Shift">
                                            {% for shift in data.shifts %}
                                            {{ shift.name }}
                                            {% if not forloop.last %}| {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td data-label="Month">
                                            {% for month in data.months %}
                                            {{ month.name }}
                                            {% if not forloop.last %}| {% endif %}
                                            {% endfor %}
                                        </td>

                                        <td data-label="Pay. Fee">{{ data.invoice.total_amount }}</td>
                                        <td data-label="Pay. Date">{{ data.invoice.issue_date }}</td>
                                        <td data-label="Due Date">{{ data.invoice.due_date }}</td>
                                        <td data-label="Pay. Status">{{ data.invoice.is_active|yesno:"Paid,Not Paid" }}</td>
                                        <td data-label="Inv. Id"><a href="/students/invoice_student/{{ data.invoice.slug }}">{{ data.invoice.invoice_id }}</a></td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {%endif%}
        </div>

        <!-- Student Demo -->

        {% comment %} <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Student</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="basic-datatables4" class="display table table-striped table-hover">
                                <thead class="dashboard-thead">
                                    <tr>
                                        <th>S.no</th>
                                        <th>Invoice ID</th>
                                        <th>Student Name</th>
                                        <th>Shift</th>
                                        <th>Month</th>
                                        <th>Payment Date</th>
                                        <th>Payment Status</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr>
                                        <th>1</th>
                                        <td>1234</td>
                                        <td>John Doe</td>
                                        <td>Morning</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>
                                    <tr>
                                        <th>2</th>
                                        <td>5678</td>
                                        <td>Yash Bendwal</td>
                                        <td>Evening</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Student</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="basic-datatables5" class="display table table-striped table-hover">
                                <thead class="dashboard-thead">
                                    <tr>
                                        <th>S.no</th>
                                        <th>Invoice ID</th>
                                        <th>Student Name</th>
                                        <th>Shift</th>
                                        <th>Month</th>
                                        <th>Payment Date</th>
                                        <th>Payment Status</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr>
                                        <th>1</th>
                                        <td>1234</td>
                                        <td>John Doe</td>
                                        <td>Morning</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>
                                    <tr>
                                        <th>2</th>
                                        <td>5678</td>
                                        <td>Yash Bendwal</td>
                                        <td>Evening</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div> {% endcomment %}



        {% elif role == "manager" %}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Library List</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="basic-datatables" class="display table table-striped table-hover">
                            <thead class="dashboard-thead">
                                <tr>
                                    <th>Library Name</th>
                                    <th>Library Owner Name</th>
                                    <th>Library Mobile</th>
                                    <th>Library Email</th>
                                    <th>Library Address</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in library_data %}
                                <tr>
                                    <td data-label="Lib Name"><a href="#">{{data.library_name}}</a></td>
                                    <td data-label="Lib. owner Name">{{data.user.first_name}} {{data.user.last_name}}</td>
                                    <td data-label="Mobile">{{data.librarian_phone_num}}</td>
                                    <td data-label="Email">{{data.user.email}}</td>
                                    <td data-label="Address">{{data.librarian_address}}</td>
                                    <td data-label="Status">{{data.is_librarian|yesno:"Approved,Not Approved"}}</td>
                                    <td>
                                        <a href="/manager/approve/{{data.slug}}"
                                            class="btn btn-success btn-sm">Approve</a>
                                        <a href="/manager/reject/{{data.slug}}" class="btn btn-danger btn-sm">Reject</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>



        <!-- Demo Table -->
{% comment %}
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Library Payment Subscription Plan</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="basic-datatables4" class="display table table-striped table-hover">
                                <thead class="dashboard-thead">
                                    <tr>
                                        <th>S.no</th>
                                        <th>Invoice ID</th>
                                        <th>Student Name</th>
                                        <th>Shift</th>
                                        <th>Month</th>
                                        <th>Payment Date</th>
                                        <th>Payment Status</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr>
                                        <th>1</th>
                                        <td>1234</td>
                                        <td>John Doe</td>
                                        <td>Morning</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>
                                    <tr>
                                        <th>2</th>
                                        <td>5678</td>
                                        <td>Yash Bendwal</td>
                                        <td>Evening</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Library Payment Subscription Plan</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="basic-datatables5" class="display table table-striped table-hover">
                                <thead class="dashboard-thead">
                                    <tr>
                                        <th>S.no</th>
                                        <th>Invoice ID</th>
                                        <th>Student Name</th>
                                        <th>Shift</th>
                                        <th>Month</th>
                                        <th>Payment Date</th>
                                        <th>Payment Status</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr>
                                        <th>1</th>
                                        <td>1234</td>
                                        <td>John Doe</td>
                                        <td>Morning</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>
                                    <tr>
                                        <th>2</th>
                                        <td>5678</td>
                                        <td>Yash Bendwal</td>
                                        <td>Evening</td>
                                        <td>June</td>
                                        <td>29/06/2024</td>
                                        <td>Confirm</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div> {% endcomment %}




        {% elif role == "librarycommander" %}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Manager List</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="basic-datatables" class="display table table-striped table-hover">
                            <thead class="dashboard-thead">
                                <tr>
                                    <th>Manager Name</th>
                                    <th>Manager Mobile</th>
                                    <th>Manager Email</th>
                                    <th>Manager Address</th>
                                    <th>Status</th>

                                </tr>
                            </thead>
                            <tbody>
                                {% for data in manager_data %}
                                <tr>
                                    <td><a href="">{{data.user.first_name}} {{data.user.last_name}}</a></td>
                                    <td>{{data.manager_phone_num}}</td>
                                    <td>{{data.user.email}}</td>
                                    <td>{{data.manager_address}}</td>
                                    <td>{{data.is_manager|yesno:"Approved,Not Approved"}}</td>

                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Library List</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="basic-datatables2" class="display table table-striped table-hover">
                            <thead class="dashboard-thead">
                                <tr>
                                    <th>Library Name</th>
                                    <th>Library Owner Name</th>
                                    <th>Library Mobile</th>
                                    <th>Library Email</th>
                                    <th>Library Address</th>
                                    <th>Status</th>

                                </tr>
                            </thead>
                            <tbody>
                                {% for data in library_data %}
                                <tr>
                                    <td><a href="">{{data.library_name}}</a></td>
                                    <td>{{data.user.first_name}} {{data.user.last_name}}</td>
                                    <td>{{data.librarian_phone_num}}</td>
                                    <td>{{data.user.email}}</td>
                                    <td>{{data.librarian_address}}</td>
                                    <td>{{data.is_librarian|yesno:"Approved,Not Approved"}}</td>

                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}






        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
            <!-- <p>Developed with passion by Librainian</p> -->
        </div>
    </div>

    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- data table -->
    <script>
        // Show success message and hide after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function (alert) {
                setTimeout(function () {
                    alert.style.transition = 'opacity 1s';
                    alert.style.opacity = '0';
                    setTimeout(function () {
                        alert.style.display = 'none';
                    }, 1000);
                }, 3000);
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            $("#basic-datatables").DataTable({});
            $("#basic-datatables2").DataTable({});
            $("#basic-datatables3").DataTable({});
            $("#basic-datatables4").DataTable({});
            $("#basic-datatables5").DataTable({});

            $("#multi-filter-select").DataTable({
                pageLength: 5,
                initComplete: function () {
                    this.api()
                        .columns()
                        .every(function () {
                            var column = this;
                            var select = $(
                                '<select class="form-select"><option value=""></option></select>'
                            )
                                .appendTo($(column.footer()).empty())
                                .on("change", function () {
                                    var val = $.fn.dataTable.util.escapeRegex($(this).val());

                                    column
                                        .search(val ? "^" + val + "$" : "", true, false)
                                        .draw();
                                });

                            column
                                .data()
                                .unique()
                                .sort()
                                .each(function (d, j) {
                                    select.append(
                                        '<option value="' + d + '">' + d + "</option>"
                                    );
                                });
                        });
                },
            });

            // Add Row
            $("#add-row").DataTable({
                pageLength: 5,
            });

            var action =
                '<td> <div class="form-button-action"> <button type="button" data-bs-toggle="tooltip" title="" class="btn btn-link btn-primary btn-lg" data-original-title="Edit Task"> <i class="fa fa-edit"></i> </button> <button type="button" data-bs-toggle="tooltip" title="" class="btn btn-link btn-danger" data-original-title="Remove"> <i class="fa fa-times"></i> </button> </div> </td>';

            $("#addRowButton").click(function () {
                $("#add-row")
                    .dataTable()
                    .fnAddData([
                        $("#addName").val(),
                        $("#addPosition").val(),
                        $("#addOffice").val(),
                        action,
                    ]);
                $("#addRowModal").modal("hide");
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            // Initialize DataTable
            $('#userTable').DataTable();

            // Initialize Charts
            var ctxMonthlySales = document.getElementById('monthlySalesChart').getContext('2d');
            var monthlySalesChart = new Chart(ctxMonthlySales, {
                type: 'line',
                data: {
                    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                    datasets: [{
                        label: 'Sales',
                        data: [12, 19, 3, 5, 2, 3, 7],
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            var ctxUserGrowth = document.getElementById('userGrowthChart').getContext('2d');
            var userGrowthChart = new Chart(ctxUserGrowth, {
                type: 'bar',
                data: {
                    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                    datasets: [{
                        label: 'User Growth',
                        data: [5, 10, 5, 2, 20, 30, 45],
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>

    <script>
         
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">

    <!-- Global Loader Script -->
     
</body>

</html>