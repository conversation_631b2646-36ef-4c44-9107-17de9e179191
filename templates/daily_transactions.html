    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar -->
     
    {% include "sidebar.html" %}
    
    <br>

    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 30rem;">
            DASHBOARD / DAILY TRANSACTIONS
        </div>
    </div>

    <!-- container -->


    <div class="container-fluid">
        <div class="row">
            <div class="col-12 col-lg-12">
                <div class="row">
                    <div class="col-12 col-lg-6">
                        <div class="card" style="background: #fff !important;">
                            <div class="card-header card_heading">
                                Daily Transaction Summary
                            </div>
                            <div class="card-body">
                                <!-- Displaying messages -->
                                {% if messages %}
                                {% for message in messages %}
                                <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                    {{ message }}
                                </div>
                                {% endfor %}
                                {% endif %}

                                <!-- Form to submit daily transaction summary -->
                                <form method="post">
                                    {% csrf_token %}

                                    <!-- Opening Balance -->
                                    <div class="form-group row">
                                        <label for="opening_balance" class="col-sm-4 col-form-label">Opening
                                            Balance:</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="opening_balance" name="opening_balance"
                                                class="form-control" value="{{ opening_balance }}">
                                        </div>
                                    </div>

                                    <!-- Other Income (Cash) -->
                                    <div class="form-group row">
                                        <label for="other_income_cash" class="col-sm-4 col-form-label">Other Income
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="other_income_cash" name="other_income_cash"
                                                class="form-control" value="{{ other_income_cash }}">
                                        </div>
                                    </div>

                                    <!-- Cash Deposit -->
                                    <div class="form-group row">
                                        <label for="deposit" class="col-sm-4 col-form-label">Cash Deposit:</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="deposit" name="deposit" class="form-control"
                                                value="{{ deposit }}">
                                        </div>
                                    </div>

                                    <!-- Sales Return (Cash) -->
                                    <div class="form-group row">
                                        <label for="sales_return_cash" class="col-sm-4 col-form-label">Sales Return
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="sales_return_cash" name="sales_return_cash"
                                                class="form-control" value="{{ sales_return_cash }}">
                                        </div>
                                    </div>

                                    <!-- Other Expenses (Cash) -->
                                    <div class="form-group row">
                                        <label for="other_expenses_cash" class="col-sm-4 col-form-label">Other Expenses
                                            (Cash):</label>
                                        <div class="col-sm-8">
                                            <input type="text" id="other_expenses_cash" name="other_expenses_cash"
                                                class="form-control" value="{{ other_expenses_cash }}">
                                        </div>
                                    </div>

                                    <!-- Save Button -->
                                    <div class="form-group row">
                                        <div class="col-sm-12 text-center">
                                            <button type="submit" class="btn btn-md btn-md-lg" style="background: #294282 !important; color: white;">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="container my-3">
                            <div class="d-flex justify-content-center">
                                {%if role != 'sublibrarian'%}

                                <a href="/librarian/transaction-report/" class="btn btn-md btn-md-lg" style="background: #294282 !important; color: white;">Go to
                                    Transaction Report</a>
                                {%endif%}
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="card" style="background: #fff !important;">
                            <div class="card-header card_heading">
                                Cash Transaction Summary
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Value</th>
                                        </tr>
                                    </thead>
                                    <tbody class="overflow-hidden">
                                        <!-- Opening Balance -->
                                        <tr>
                                            <td>Opening Balance</td>
                                            <td>{{ transactions.opening_balance }}</td>
                                        </tr>

                                        <!-- Other Income (Cash) -->
                                        <tr>
                                            <td>Other Income (Cash)</td>
                                            <td>{{ transactions.other_income_cash }}</td>
                                        </tr>

                                        <!-- Cash Deposit -->
                                        <tr>
                                            <td>Cash Deposit</td>
                                            <td>{{ transactions.deposit }}</td>
                                        </tr>

                                        <!-- Sales Return (Cash) -->
                                        <tr>
                                            <td>Sales Return (Cash)</td>
                                            <td>{{ transactions.sales_return_cash }}</td>
                                        </tr>

                                        <!-- Other Expenses (Cash) -->
                                        <tr>
                                            <td>Other Expenses (Cash)</td>
                                            <td>{{ transactions.other_expenses_cash }}</td>
                                        </tr>

                                        <tr class="table_fansy_row">
                                            <td>Today Closing Balance</td>
                                            <td>{{ transactions.closing_balance_cash }}</td>
                                        </tr>
                                        <tr class="">
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10">
                        <div class="card mt-3 mt-md-4 mt-lg-5" style="background: #fff !important;">
                            <div class="card-header card_heading">
                                Overall Transaction Summary
                            </div>
                            <div class="card-body">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Value</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Opening Balance -->
                                        <tr>
                                            <td>Opening Balance</td>
                                            <td>{{ transactions.opening_balance }}</td>
                                        </tr>

                                        <!-- Sales Student Fees Collection (Online)-->
                                        <tr>
                                            <td>Sales (Online)</td>
                                            <td>{{ transactions.sales_online }}</td>
                                        </tr>

                                        <tr>
                                            <td>Sales (Cash)</td>
                                            <td>{{ transactions.sales_cash }}</td>
                                        </tr>

                                        <!-- Other Income (Cash) -->
                                        <tr>
                                            <td>Other Income (Cash)</td>
                                            <td>{{ transactions.other_income_cash }}</td>
                                        </tr>


                                        <!-- Other Income (Online) -->
                                        <tr>
                                            <td>Other Income (Online)</td>
                                            <td>{{ transactions.other_income_online }}</td>
                                        </tr>

                                        <!-- Cash Deposit -->
                                        <tr>
                                            <td>Cash Deposit</td>
                                            <td>{{ transactions.deposit }}</td>
                                        </tr>

                                        <!-- Sales Return (Cash) -->
                                        <tr>
                                            <td>Sales Return (Cash)</td>
                                            <td>{{ transactions.sales_return_cash }}</td>
                                        </tr>

                                        <!-- Sales Return (Online) -->
                                        <tr>
                                            <td>Sales Return (Online)</td>
                                            <td>{{ transactions.sales_return_online }}</td>
                                        </tr>

                                        <!-- Other Expenses (Cash) -->
                                        <tr>
                                            <td>Other Expenses (Cash)</td>
                                            <td>{{ transactions.other_expenses_cash }}</td>
                                        </tr>

                                        <!-- Other Expenses (Online) -->
                                        <tr>
                                            <td>Other Expenses (Online)</td>
                                            <td>{{ transactions.sales_return_online }}</td>
                                        </tr>

                                        <tr class="table_fansy_row">
                                            <td>Today Closing Balance</td>
                                            <td>{{ transactions.closing_balance_online }}</td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy"
                        style="filter: invert(0.5) brightness(0);">
                    <!-- <p>Developed with passion by Librainian</p> -->
                </div>
            </div>
        </div>
    </div>








    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Show success message and hide after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function (alert) {
                setTimeout(function () {
                    alert.style.transition = 'opacity 1s';
                    alert.style.opacity = '0';
                    setTimeout(function () {
                        alert.style.display = 'none';
                    }, 1000);
                }, 10000);
            });
        });
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>

    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css">
</body>

</html>