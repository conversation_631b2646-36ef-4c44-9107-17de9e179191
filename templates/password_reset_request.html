<!DOCTYPE html>
<html lang="en">
<head>
    {% load bootstrap5 %}
    {% bootstrap_css %}
    {% bootstrap_javascript %}
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index,follow,max-image-preview:large">
    <link rel="canonical" href="https://www.librainian.com/password-reset/">
    <meta name="keywords" content="password reset, recover account, library management system, forgot password, account recovery, reset password, library software, library app, secure password reset, library account access">
    <meta name="description" content="Reset your password for Librainian - The #1 Library Management System. Enter your email to receive a secure OTP and regain access to your account. Quick and secure password recovery.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Password Reset | Librainian - Modern Library Management System">
    <meta property="og:description" content="Reset your password by entering your email to receive a secure OTP. Regain access to your Librainian account quickly and securely.">
    <meta property="og:url" content="https://www.librainian.com/password-reset/">
    <meta property="og:image" content="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-registration/draw1.webp">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Password Reset | Librainian - Modern Library Management System">
    <meta name="twitter:description" content="Request an OTP to reset your password and regain access to your Librainian account. Secure and easy password recovery.">
    <meta name="twitter:image" content="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-registration/draw1.webp">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-01-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <!-- Mobile App Capability -->
    <meta name="theme-color" content="#042299">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Librainian">

    <title>Password Reset | Librainian - Modern Library Management System</title>

    <!-- Favicon -->
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Password Reset | Librainian - Modern Library Management System",
        "description": "Reset your password for Librainian - The #1 Library Management System. Enter your email to receive a secure OTP and regain access to your account.",
        "url": "https://www.librainian.com/password-reset/",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Password Reset",
                    "item": "https://www.librainian.com/password-reset/"
                }
            ]
        }
    }
    </script>

    <!-- Password Reset Form Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Librainian Password Reset Form",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "description": "Password reset form for Librainian - The #1 Library Management System",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "INR"
        },
        "potentialAction": {
            "@type": "ResetPasswordAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://www.librainian.com/password-reset/",
                "inLanguage": "en-US",
                "actionPlatform": [
                    "http://schema.org/DesktopWebPlatform",
                    "http://schema.org/MobileWebPlatform"
                ]
            }
        }
    }
    </script>

         <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      

    <style>
              body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>
</head>
<body>

    <section class="vh-100" style="background-color: #eee;">
        <div class="container h-100">
            <div class="row d-flex justify-content-center align-items-center h-100">
                <div class="col-lg-12 col-xl-11">
                    <div class="card text-black" style="border-radius: 25px;">
                        <div class="card-body p-md-5">
                            <div class="row justify-content-center">
                                <div class="col-md-10 col-lg-6 col-xl-5 order-2 order-lg-1">
                                    <p class="text-center h1 fw-bold mb-5 mx-1 mx-md-4 mt-4">Password Reset</p>

                                    {% if messages %}
                                        <div id="messages">
                                            {% for message in messages %}
                                                <div class="alert alert-danger" role="alert">
                                                    {{ message }}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <form method="post" id="resetForm">
                                        {% csrf_token %}
                                        <div class="form-outline mb-4">
                                            <label class="form-label" for="email">Email:</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                                <input type="email" id="email" name="email" class="form-control form-control-lg" placeholder="Enter your email address" required>
                                            </div>
                                        </div>
                                        <div id="error-message" class="text-danger mb-3" style="display: none;"></div>
                                        <div class="d-flex justify-content-center">
                                            <button type="submit" class="btn btn-dark btn-lg btn-block">Request OTP</button>
                                        </div>
                                        <div class="d-flex justify-content-center mt-3">
                                            <a href="/librarian/login/" class="small text-muted">Wanna login?</a>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-10 col-lg-6 col-xl-7 d-flex align-items-center order-1 order-lg-2">
                                    <img src="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-registration/draw1.webp"
                                    class="img-fluid" alt="reset password" loading="lazy">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 col-lg-6 col-xl-7 d-flex align-items-center order-1 order-lg-2 p-5 ms-auto justify-content-end">
                    <a href="/blogs/p/about" class="small text-muted" style="font-size: 1.1rem; margin-right: 20px; text-decoration: none; transition: color 0.3s;">
                      About Us
                    </a>
                    <a href="/blogs/p/privacy-policy" class="small text-muted" style="font-size: 1.1rem; margin-right: 20px; text-decoration: none; transition: color 0.3s;">
                      Privacy Policy
                    </a>
                    <a href="/blogs/p/termsofuse" class="small text-muted" style="font-size: 1.1rem; text-decoration: none; transition: color 0.3s;">
                      Terms of Use
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('resetForm');
            const errorMessage = document.getElementById('error-message');

            form.addEventListener('submit', function (event) {
                event.preventDefault(); // Prevent default form submission

                const email = document.getElementById('email').value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (!emailRegex.test(email)) {
                    showError('Please enter a valid email address.');
                    return;
                }

                const sanitizedEmail = email.replace(/['";<>]/g, ''); // Sanitize the email
                console.log('Validated Email:', sanitizedEmail);

                // Hide the error message and allow form submission
                errorMessage.style.display = 'none';

                alert('Form submitted successfully with sanitized email!');
                form.submit(); // Submit the form
            });

            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
        });
    </script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
</body>
</html>
