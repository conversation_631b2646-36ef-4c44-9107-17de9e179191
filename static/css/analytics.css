body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0.5rem;
    font-family: 'Comfortaa', sans-serif;
    background: #cee3e0 !important;
}

.card-primary {
    background-color: #1572E8;
    color: white;
    border-radius: 15px;
}

.card-info {
    background-color: #48ABF7;
    color: white;
    border-radius: 15px;
}

.card-success {
    background-color: #31CE36;
    color: white;
    border-radius: 15px;
}

.card-secondary {
    background-color: #6861CE;
    color: white;
    border-radius: 15px;
}

.icon-big {
    font-size: 3rem;
}

.card-round {
    border-radius: 15px;
}



.dropdown-content {
    display: none;
    position: absolute;
    background-color: #343a40;
    color: white;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.dropdown-content a {
    color: white;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.show {
    display: block;
}


.main-content {
    margin-left: 250px;
    padding: 4px;
    transition: all 0.3s;
}

/* .navbar {
    transition: all 0.3s;
    margin-bottom: 20px;
} */
.navbar {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    background-color: #f0f0f0;
    box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
}

.nav-item a img {
    width: 25px;
    padding-bottom: 4px;
}

#dropdown-menu {
    width: 350px;
    padding: 20px;

}

hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
    margin-left: 110px;
    padding-top: 10px;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.profile-img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    /* margin-bottom: 15px; */
    border-radius: 50%;
    margin-left: 10px;
}



.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}

/* Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    background-color: #f1f8ff;
    padding: 10px 0;
    z-index: 1000;
}

.footer-menu a,
.footer-menu .settings-link {
    color: #000000;
    font-size: 24px;
    text-align: center;
    text-decoration: none;
    position: relative;
}

.footer-menu a.active i,
.footer-menu .settings-link.active i {
    color: #020202;
}

.footer-menu .settings-link .submenu {
    display: none;
    position: absolute;
    bottom: 65px;
    left: -250px;
    background-color: #ffffff;
    border: 1px solid #000000;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
    padding: 10px;
    z-index: 1001;
    white-space: nowrap;
}

.footer-menu .settings-link a {
    display: block;
    color: #000000;
    padding: 5px 10px;
    text-decoration: none;
    text-align: start;
    margin-top: 10px;
}

.footer-menu .settings-link a:hover {
    background-color: #000000;
    color: white;
    border-radius: 3px;
}

.footer-menu .settings-link.active {
    display: block;
}

.submenu {
    display: none;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 0 10px rgb(192, 221, 253);
    z-index: 1000;
    left: 0;
    margin-top: 5px;
    padding: 10px;
    height: auto;
    width: 310px;
}

.sub-submenu {
    display: none;
    position: absolute;
    left: 100%;
    /* Position sub-submenu to the right of submenu */
    top: 0;
    margin-top: -10px;
    /* Adjust top margin as needed */
    padding: 5px 0;
    /* Add padding to sub-submenu */
    background-color: #f9f9f9;
    /* Adjust background color */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
    /* Optional: Add shadow for better visibility */
}

.settings-link:hover .submenu,
.settings-link:focus .submenu {
    display: block;
}

.sub-submenu {
    display: none;
}

.submenu-item:hover .sub-submenu,
.submenu-item:focus .sub-submenu {
    display: block;
}

.btn-success {
    background-color: white;
    color: #000000;
}

.btn-success:hover {
    background-color: green;
    color: white;
}

.btn-danger {
    background-color: white;
    color: rgb(0, 0, 0);
}

.btn-danger:hover {
    background-color: rgb(230, 17, 17);
    color: rgb(255, 255, 255);
}

.btn-link {
    color: #000000;
    padding-left: 2px;
    margin-bottom: -12px;
}

.btn-link:hover {
    color: #000000;
    text-decoration: none;
}

.submenu a {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #333;
    /* Example text color */
}

.submenu a:hover {
    background-color: #f0f0f0;
    /* Example hover background color */
}

.footer {
    /* background-color: #dad9df; */
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 10px;
    color: #677488;
    opacity: 60%;

}

.footer img {
    width: 300px;
    padding-top: 10px;
    opacity: 60%;

}

/* 
.footer p {
    font-size: 12px;
    padding-top: 5px;
} */

@media (max-width:768px) {
    .footer {
        margin-bottom: 50px;
    }

    .footer img {
        width: 60%;
        padding-bottom: 60px;
    }

}

#notificationBtn {
    display: none;
}
.icon-text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.icon-text-wrapper i {
    font-size: 24px;
    /* Adjust the icon size as needed */
    margin-bottom: 5px;
    /* Space between icon and text */
}

.dashboard-text {
    font-size: 12px;
    /* Match the font size to the icon size */
    line-height: 24px;
    /* Set the line-height to match the icon's height */
}

@media only screen and (max-width: 767px) {
    .small_p p{
    font-size: 12px !important;
}
}

.analitics_container p {
    font-size: 1rem;
}

.analitics_container i {
    font-size: 1.9rem;
}

.col-stats {
    padding: 0;
    margin: 0;
}

