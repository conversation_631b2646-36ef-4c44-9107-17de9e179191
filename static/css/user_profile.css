body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
    padding-bottom: 50vh;
}

.profile_section_card{
    background-color: #fff !important;
}

.main-content {
    margin-left: 250px;
    padding: 4px;
    transition: all 0.3s;
}

.thead-theme {
    background-color: #28345a;
    color: #9bc6bf;
}

.profile_section {
    margin: 1rem 0.5rem;
    background-color: #9bc6bf;
    color: #28345a;
    border-radius: 1rem;
}

hr {
    width: 100%;
    color: black;
    margin-bottom: 0px;
}

.viewbtn {
    background-color: #050505;
    width: 90px;
    font-size: 11px;
    color: #fff;
}

.viewbtn:hover {
    background-color: white;
    color: black;
    border: 1px solid black;
}


.profile-img {
    width: 70px;
    height: 70px;
    object-fit: cover;
    /* margin-bottom: 15px; */
    border-radius: 50%;
    margin-left: 10px;
}

.form-control:focus {
    /* border: none;
    outline: none; */
    box-shadow: none;
}


.card_container p {
    font-size: 1rem;
}


/* for the QR */

.qr_body {
    margin: 0.5rem 0rem 2rem 0rem;
    background: linear-gradient(135deg, #1e293b, #3b82f6);
    border-radius: 1rem;
    color: #ffffff;
    font-family: 'Roboto', sans-serif;
}

.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 1rem !important;
   
}

.qr-card {
    margin: 2rem 1rem;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    width: 90%;
    background: #ffffff;
    color: #1e293b;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.qr-card h1 {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 20px;
}
.qr-card img {
    border: 5px solid #3b82f6;
    border-radius: 10px;
    margin-top: 20px;
    max-width: 100%;
    height: auto;
}
.qr-card p {
    font-size: 1rem;
    margin-top: 15px;
    color: #6b7280;
}
.download-btn {
    padding: 10px 20px;
    font-size: 1rem;
    background-color: #3b82f6;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}
.download-btn:hover {
    background-color: #1d4ed8;
}


@media only screen and (min-width: 767px) {
    
.qr_body {
    margin: 1.7rem 0rem 2rem 0rem; 
}

.profile_section {
    padding: 0.5rem;
}

}