<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    
    <title>Add Sublibrarian | Librainian</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tiny5&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #28345a;
            --secondary-color: #cee3e0;
            --accent-color: #3f83f1;
            --text-color: #333;
            --light-text: #6c757d;
            --border-radius: 1rem;
            --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f8f9fa;
            color: var(--text-color);
        }
        
        .page-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #e0f2f1 0%, var(--secondary-color) 100%);
            padding: 2rem 0;
        }
        
        .card {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1a2542 100%);
            color: white;
            padding: 1.25rem 1.5rem;
            border-bottom: none;
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            border: 1px solid #ced4da;
            transition: all 0.2s ease-in-out;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(40, 52, 90, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }
        
        .btn-primary:hover {
            background-color: #1a2542;
            border-color: #1a2542;
            transform: translateY(-2px);
        }
        
        .btn-outline-secondary {
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }
        
        .alert {
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
        }
        
        .breadcrumb {
            background-color: transparent;
            padding: 0.5rem 0;
            margin-bottom: 1.5rem;
        }
        
        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease-in-out;
        }
        
        .breadcrumb-item a:hover {
            color: #1a2542;
            text-decoration: underline;
        }
        
        .valid {
            color: green;
        }
        
        .invalid {
            color: red;
        }
        
        .input-group-text {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
        
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
    </style>
</head>
<body>

<div class="page-container">
    <div class="container">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/librarian/dashboard/"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Add Sublibrarian</li>
            </ol>
        </nav>
        
        <!-- Alert Messages -->
        {% if messages %}
        <div id="messageContainer">
            {% for message in messages %}
            {% if message.tags == 'success' %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'error' %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'warning' %}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% elif message.tags == 'info' %}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endif %}
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card shadow">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Add Sublibrarian</h4>
                    </div>
                    <div class="card-body p-4">
                        <p class="text-muted mb-4">
                            Add a sublibrarian to help manage your library. They will have access to manage students, handle daily transactions, and assist with library operations.
                        </p>
                        
                        <form method="post" onsubmit="return checkPasswordMatch()" class="needs-validation" novalidate>
                            {% csrf_token %}
                            
                            <!-- Personal Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-user me-2"></i>Personal Information</h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="first_name" class="form-label required-field">First Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="first_name" name="first_name" class="form-control" required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                                            <div class="invalid-feedback">
                                                Please enter a valid first name (letters only).
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="last_name" class="form-label required-field">Last Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" id="last_name" name="last_name" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a last name.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Account Information Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-id-card me-2"></i>Account Information</h5>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label for="username" class="form-label required-field">Username</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-at"></i></span>
                                            <input type="text" id="username" name="username" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please choose a username.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="email" class="form-label required-field">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" id="email" name="email" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid email address.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label required-field">Phone Number</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" id="phone" name="phone" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter a valid phone number.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="address" class="form-label required-field">Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" id="address" name="address" class="form-control" required>
                                            <div class="invalid-feedback">
                                                Please enter an address.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Password Section -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3"><i class="fas fa-lock me-2"></i>Set Password</h5>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="password1" class="form-label required-field">Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" id="password1" name="password1" class="form-control" required>
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small id="passwordHelp" class="form-text text-muted">
                                            Password must be at least 8 characters with uppercase, number, and special character.
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="password2" class="form-label required-field">Confirm Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" id="password2" name="password2" class="form-control" required>
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword2">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small id="confirmPasswordHelp" class="form-text text-muted">
                                            Please confirm your password.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between mt-4">
                                <a href="/librarian/dashboard/" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary" id="registerButton" disabled>
                                    <i class="fas fa-user-plus me-2"></i>Add Sublibrarian
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Form validation and password handling
    (function() {
        'use strict';
        
        // Function to validate phone number
        function validatePhone(input) {
            input.value = input.value.replace(/\D/g, '').slice(0, 10);
        }
        
        // Function to validate password
        function validatePassword() {
            const password = document.getElementById('password1').value.trim();
            const passwordHelp = document.getElementById('passwordHelp');
            let valid = true;
            
            if (!/[A-Z]/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one uppercase letter.";
                valid = false;
            } else if (!/\d/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one number.";
                valid = false;
            } else if (!/[!@#$%^&*]/.test(password)) {
                passwordHelp.textContent = "Password must contain at least one special character (!@#$%^&*).";
                valid = false;
            } else if (password.length < 8) {
                passwordHelp.textContent = "Password must be at least 8 characters long.";
                valid = false;
            } else {
                passwordHelp.textContent = "Password is valid.";
                valid = true;
            }
            
            passwordHelp.classList.toggle('valid', valid);
            passwordHelp.classList.toggle('invalid', !valid);
            
            validateConfirmPassword();
            checkFormValidity();
        }
        
        // Function to validate confirm password
        function validateConfirmPassword() {
            const password = document.getElementById('password1').value.trim();
            const confirmPassword = document.getElementById('password2').value.trim();
            const confirmPasswordHelp = document.getElementById('confirmPasswordHelp');
            
            const isValid = password === confirmPassword && confirmPassword.length > 0;
            confirmPasswordHelp.textContent = isValid ? "Passwords match." : "Passwords do not match.";
            confirmPasswordHelp.classList.toggle('valid', isValid);
            confirmPasswordHelp.classList.toggle('invalid', !isValid);
            
            checkFormValidity();
        }
        
        // Function to check form validity
        function checkFormValidity() {
            const passwordHelp = document.getElementById('passwordHelp').classList.contains('valid');
            const confirmPasswordHelp = document.getElementById('confirmPasswordHelp').classList.contains('valid');
            const registerButton = document.getElementById('registerButton');
            
            registerButton.disabled = !(passwordHelp && confirmPasswordHelp);
        }
        
        // Function to toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const passwordField = document.getElementById(inputId);
            const toggleButton = document.getElementById(buttonId);
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            toggleButton.querySelector('i').classList.toggle('fa-eye-slash');
        }
        
        // Function to check password match on form submission
        window.checkPasswordMatch = function() {
            const password = document.getElementById('password1').value.trim();
            const confirmPassword = document.getElementById('password2').value.trim();
            if (password === confirmPassword) {
                return true; // Allow form submission
            } else {
                alert("Passwords do not match.");
                return false; // Prevent form submission
            }
        };
        
        // Function to hide alerts after 5 seconds
        function setupAlertDismissal() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        }
        
        // Set up event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Phone number validation
            const phoneInput = document.getElementById('phone');
            if (phoneInput) {
                phoneInput.addEventListener('input', function() {
                    validatePhone(this);
                });
            }
            
            // Password validation
            const passwordInput = document.getElementById('password1');
            if (passwordInput) {
                passwordInput.addEventListener('input', validatePassword);
            }
            
            // Confirm password validation
            const confirmPasswordInput = document.getElementById('password2');
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', validateConfirmPassword);
            }
            
            // Toggle password visibility
            const togglePasswordButton = document.getElementById('togglePassword');
            if (togglePasswordButton) {
                togglePasswordButton.addEventListener('click', function() {
                    togglePasswordVisibility('password1', 'togglePassword');
                });
            }
            
            const togglePassword2Button = document.getElementById('togglePassword2');
            if (togglePassword2Button) {
                togglePassword2Button.addEventListener('click', function() {
                    togglePasswordVisibility('password2', 'togglePassword2');
                });
            }
            
            // Set up alert dismissal
            setupAlertDismissal();
            
            // Bootstrap form validation
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        });
    })();
</script>
</body>
</html>
