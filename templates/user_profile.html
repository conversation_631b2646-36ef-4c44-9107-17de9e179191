<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title> Librainian </title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Favicon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
        <link rel="stylesheet" href="/static/css/user_profile.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/analytics.css">
    <link rel="stylesheet" href="/static/css/marketing_page.css">
    <link rel="stylesheet" href="/static/css/daily_transactions.css">
    <link rel="stylesheet" href="/static/css/package.css">


    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">




        <!-- disable Print Screen for Windows -->



        <!-- disable print screen for mac -->



        <!-- disabling print screen for Linux -->



        <!-- disabling inspection tool -->



    <style>
        body h1, h2, h3, h4, h5,  h6, p , ul, li, strong, em, b , s, small, span {
            font-family: 'Comfortaa', sans-serif !important;
        }

        .card {
            border-radius: 1rem;
            background: #fff !important;
        }

        table {
            border-radius: 1rem;
        }

        .table {
            border-radius: 1rem !important;

        }

        .btn {
            border-radius: 1rem !important;
        }

        input{
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
            width: 90%;
        }

        select{
            padding: 0.5rem 1rem;
            border-radius: 1rem !important;
        }

       @media only screen and (max-width: 767px) {
        .d-sm-none {
            display: none !important;
        }
       }

       body {
    -webkit-user-select: none; /* Disable text selection */
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    font-family: 'Comfortaa', sans-serif;
    padding-bottom: 50vh;
        }

        .profile_section_card{
            padding: 1rem;
            margin:1rem;
            background-color: #fff !important;
        }

        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        .thead-theme {
            background-color: #28345a;
            color: #9bc6bf;
        }

        .profile_section {
            margin: 1rem 0.5rem;
            background-color: #9bc6bf;
            color: #28345a;
            border-radius: 1rem;
        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }


        .card_container p {
            font-size: 1rem;
        }


        /* for the QR */

        .qr_body {
            margin: 0.5rem 0rem 2rem 0rem;
            background: linear-gradient(135deg, #1e293b, #3b82f6);
            border-radius: 1rem;
            color: #ffffff;
            font-family: 'Roboto', sans-serif;
        }

        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border-radius: 1rem !important;

        }

        .qr-card {
            margin: 2rem 1rem;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            width: 90%;
            background: #ffffff;
            color: #1e293b;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .qr-card h1 {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .qr-card img {
            border: 5px solid #3b82f6;
            border-radius: 10px;
            margin-top: 20px;
            max-width: 100%;
            height: auto;
        }
        .qr-card p {
            font-size: 1rem;
            margin-top: 15px;
            color: #6b7280;
        }
        .download-btn {
            padding: 10px 20px;
            font-size: 1rem;
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .download-btn:hover {
            background-color: #1d4ed8;
        }


        @media only screen and (min-width: 767px) {

        .qr_body {
            margin: 1.7rem 0rem 2rem 0rem;
        }

        .profile_section {
            padding: 0.5rem;
        }

        }

        </style>

</head>

<body>


    <!-- Sidebar -->

    {% include "sidebar.html" %}

    <section class="profile_section">



        <div class="container-fluid pt-3">
        {% if messages %}
        <div id="messageContainer">
            {% for message in messages %}
            {% if message.tags == 'success' %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {% elif message.tags == 'error' %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {% elif message.tags == 'warning' %}
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        {% elif message.tags == 'info' %}
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            {% else %}
                            <div class="alert alert-secondary alert-dismissible fade show" role="alert">
                                {% endif %}
                                {{ message }}
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}


            <div class="row">
                <div class="col-lg-5">
                    <div class="card profile_section_card" style="background-color: #ddd;">

                            {% if role == 'librarian' %}
                        <div class="card-body text-center">
                            <!-- <img src="{% if lib.image %}{{ lib.image.url }}{% else %}https://picsum.photos/150{% endif %}"
                            alt="avatar" class="img-fluid" style="width: 600px; height: 100px;" loading="lazy"> -->
                            <h5 class="my-3" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h5>
                            <p class="text-muted mb-1">{{lib.library_name}}</p>
                            <p class="text-muted mb-4" id="profileAddress">{{lib.librarian_address}}</p>


                            {% elif role == 'sublibrarian' %}
                            <!-- <img src="{% if lib.image %}{{ lib.image.url }}{% else %}https://picsum.photos/150{% endif %}"
                            alt="avatar" class="img-fluid" style="width: 600px; height: 100px;" loading="lazy">-->
                            <h5 class="my-3" id="profileName">{{superlib.user.first_name}} {{superlib.user.last_name}}</h5>
                            <p class="text-muted mb-1">{{lib.librarian.library_name}}</p>
                            <p class="text-muted mb-4" id="profileAddress">{{lib.librarian.librarian_address}}</p>

                            <!-- <img src="/static/img/about2.jpg" alt=""> -->

                            {% elif role == 'librarycommander' %}
                            <h5 class="my-3" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h5>

                            {% elif role == 'manager' %}
                            <h5 class="my-3" id="profileName">{{lib.user.first_name}} {{lib.user.last_name}}</h5>

                            {% endif %}

                            {% if role == 'librarian' %}

                            <div class="d-flex justify-content-center mb-2">
                                <a href="/{{role}}/edit-profile/" class="btn btn-dark custom-margin">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                {% comment %} <button type="button" data-mdb-button-init data-mdb-ripple-init
                                    class="btn btn-outline-dark">
                                    Message
                                </button> {% endcomment %}
                            </div>
                            {% endif %}

                            {% if role == 'librarycommander' %}

                            <div class="d-flex justify-content-center mb-2">
                                <a href="/{{role}}/edit-profile" class="btn btn-dark custom-margin">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                {% comment %} <button type="button" data-mdb-button-init data-mdb-ripple-init
                                    class="btn btn-outline-dark">
                                    Message
                                </button> {% endcomment %}
                            </div>
                            {% endif %}

                            {% if role == 'manager' %}

                            <div class="d-flex justify-content-center mb-2">
                                <a href="/{{role}}/edit-profile" class="btn btn-dark custom-margin">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                {% comment %} <button type="button" data-mdb-button-init data-mdb-ripple-init
                                    class="btn btn-outline-dark">
                                    Message
                                </button> {% endcomment %}
                            </div>
                            {% endif %}


                        </div>
                    </div>
                    {% comment %} <div class="card profile_section_card rounded-3 mb-4 mb-lg-0" style="background-color: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #ddd;">
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush rounded-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fas fa-globe fa-lg text-warning"></i>
                                    <p class="mb-0">https://mdbootstrap.com</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-github fa-lg text-body"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-twitter fa-lg" style="color: #55acee;"></i>
                                    <p class="mb-0">@mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-instagram fa-lg" style="color: #ac2bac;"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center p-3">
                                    <i class="fab fa-facebook-f fa-lg" style="color: #3b5998;"></i>
                                    <p class="mb-0">mdbootstrap</p>
                                </li>
                            </ul>
                        </div>
                    </div> {% endcomment %}
<!-- for the QR -->

<!-- QR end                -->

                {% if role == 'librarian' %}
                    <div class="qr_body">
                        <div class="container qr-code-container">
                            <div class="qr-card">
                                <h1>QR Code for {{ lib.library_name }}</h1>
                                <img id="qrImage" src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid">
                                <p>Scan the QR code above to view the details.</p>
                                <button class="download-btn" onclick="downloadQR()">Download QR Code</button>
                            </div>
                        </div>
                    </div>



                <div class="card" style="border: 1px solid #ddd; overflow: hidden; margin-bottom: 1.5rem;">
                    <div style="background-color: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #ddd;">
                        <h5 style="margin: 0; font-size: 1.25rem;" class="text-center">Transaction History</h5>
                    </div>
                    <div style="padding: 0; background-color: white;">
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; margin-bottom: 0; font-size: 0.875rem; border-collapse: collapse;">
                                <thead>
                                    <tr>
                                        <th style="padding: 0.75rem; vertical-align: bottom; border-bottom: 2px solid #dee2e6; text-align: left;">Date</th>
                                        <th style="padding: 0.75rem; vertical-align: bottom; border-bottom: 2px solid #dee2e6; text-align: left;">Description</th>
                                        <th style="padding: 0.75rem; vertical-align: bottom; border-bottom: 2px solid #dee2e6; text-align: left;">Amount</th>
                                        <th style="padding: 0.75rem; vertical-align: bottom; border-bottom: 2px solid #dee2e6; text-align: left;">Status</th>
                                    </tr>
                                </thead>
                                {% for data in transaction %}
                                <tbody>
                                    <tr>
                                        <td style="padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6;">{{data.created_at|date:"d-m-Y"}}</td>
                                        <td style="padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6;">{{data.note}}</td>
                                        <td style="padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6;">{{data.amount}}</td>
                                        <td style="padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6;">
                                            {% if data.is_credit %}
                                                <span style="color: #28a745;">Credit</span>
                                            {% else %}
                                                <span style="color: #dc3545;">Debit</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                </div>
                <div class="col-lg-7 card_container">
                    <div class="card mb-4">
                        <div class="card-body p-2">
                            {% if role == 'librarian' %}
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Library Name: </p>
                                </div>
                                <div class="col-sm-7 pt-3">
                                    <p class="text-muted mb-0" id="libraryName">{{lib.library_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Name</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="fullName">{{lib.user.first_name}}
                                        {{lib.user.last_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Email</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="email">{{lib.user.email}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Mobile</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="mobile">{{lib.librarian_phone_num}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Address</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address">{{lib.librarian_address}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Library Google Map</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <a href={{lib.google_map_url}} class="text-muted mb-0" id="google_map_url">{{lib.google_map_url}}</a>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Membership Package</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address">{{membership.plan.name}} | {{membership.plan.price}} </p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Start Date and Expiry Date</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address">{{membership.start_date}} | {{membership.expiry_date}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Wallet Balance</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address"> <i class="fa-solid fa-coins"></i> {{wallet.balance}} Points </p>
                                </div>
                            </div>

                            {% elif role == 'sublibrarian' %}
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Sub-librarian Name</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="libraryName">{{lib.librarian.library_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Name</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="fullName">{{lib.user.first_name}}
                                        {{lib.user.last_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Email</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="email">{{lib.user.email}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Mobile</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="mobile">{{lib.sublibrarian_phone_num}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Address</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="address">{{lib.sublibrarian_address}}</p>
                                </div>

                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Membership Package</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address">{{membership.plan.name}} | {{membership.plan.price}} </p>
                                </div>
                            </div>
                            <hr>

                            <div class="row">
                                <div class="col-sm-4 pt-3">
                                    <p class="mb-0">Start Date and Expiry Date</p>
                                </div>
                                <div class="col-sm-8 pt-3">
                                    <p class="text-muted mb-0" id="address">{{membership.start_date}} | {{membership.expiry_date}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Address</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="address">{{lib.sublibrarian_address}}</p>
                                </div>

                            </div>
                            {% comment %} <div class="d-flex justify-content-center mt-4 mb-2">
                                <a href="/{{role}}/edit-profile/" class="btn btn-dark custom-margin">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </div> {% endcomment %}


                            {% elif role == 'librarycommander' %}
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Name</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="fullName">{{lib.user.first_name}}
                                        {{lib.user.last_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Email</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="email">{{lib.user.email}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Mobile</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="mobile">{{lib.librarycommander_phone_num}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Address</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="address">{{lib.librarycommander_address}}</p>
                                </div>
                            </div>

                            {% elif role == 'manager' %}

                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Name</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="fullName">{{lib.user.first_name}}
                                        {{lib.user.last_name}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Email</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="email">{{lib.user.email}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Mobile</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="mobile">{{lib.manager_phone_num}}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4">
                                    <p class="mb-0">Address</p>
                                </div>
                                <div class="col-sm-8">
                                    <p class="text-muted mb-0" id="address">{{lib.manager_address}}</p>
                                </div>
                            </div>

                             {% endif %}
                        </div>
                    </div>

                    {% if role == 'librarian' %}
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title text-center">Sub-Librarian Data</h4>
                                </div>
                                <div class="card-body" id="sub-lib">
                                    <div class="table-responsive card p-2" style="border-radius: 1rem; overflow: hidden;">
                                        <table id="basic-datatables" class="display table table-striped table-hover" style="font-size: 0.9em;">
                                            <thead class="thead-theme">
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Phone Number</th>
                                                    <th>Email</th>
                                                    <th>Address</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for data in sublibrarian %}
                                                <tr>
                                                    <td data-label="Name">{{ data.user.first_name }} {{ data.user.last_name }}</td>
                                                    <td data-label="Phone Number">{{ data.sublibrarian_phone_num }}</td>
                                                    <td data-label="Email">{{ data.user.email }}</td>
                                                    <td data-label="Address">{{ data.sublibrarian_address }}</td>
                                                    <td data-label="Action">
                                                        <!-- Edit Button -->
                                                        <a href="/librarian/sublibrarian/update/{{data.id}}" class="btn btn-success btn-sm">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <!-- Delete Button -->
                                                        <a href="/librarian/sublibrarian/delete/{{data.id}}" class="btn btn-danger btn-sm">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                                                </div>
                                <!-- <div class="container d-md-none">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group d-flex align-items-center">
                                                <label for="entries" class="mr-2 mb-0">Show entries:</label>
                                                <select id="entries" class="form-control form-control-sm w-auto">
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group d-flex align-items-center">
                                                <label for="search" class="mr-2 mb-0">Search:</label>
                                                <input type="text" id="search" class="form-control form-control-sm w-auto">
                                            </div>
                                        </div>
                                    </div>

                                    <div id="studentCards" class="student-cards ">
                                        {% for data in sublibrarian %}
                                        <div class="card mb-3 student-card status-{{ s.color|default:'normal' }}">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">{{ data.user.first_name }} {{ data.user.last_name }}</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <p class="card-text">
                                                            <i class="fas fa-book mr-2"></i>
                                                            <strong>Phone Number:</strong> {{ data.sublibrarian_phone_num }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-phone mr-2"></i>
                                                            <strong>Email:</strong> {{ data.user.email }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-address-card mr-2"></i>
                                                            <strong>Address:</strong> {{ data.sublibrarian_address }}
                                                        </p>
                                                        <p class="card-text">
                                                            <i class="fas fa-cogs mr-2"></i>
                                                            <strong>Action:</strong>
                                                             Edit Button 
                                                            <a href="/librarian/sublibrarian/update/{{data.id}}" class="btn btn-success btn-sm ml-2">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                             Delete Button 
                                                            <a href="/librarian/sublibrarian/delete/{{data.id}}" class="btn btn-danger btn-sm ml-2">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </a>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                        </div> -->
                                        {% endfor %}
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <nav aria-label="Page navigation">
                                                <ul class="pagination justify-content-center">
                                                    <!-- Pagination will be dynamically generated -->
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% endif %}


                </div>
            </div>
        </div>
    </section>


    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadQR() {
            const qrImage = document.getElementById('qrImage');
            const link = document.createElement('a');
            link.href = qrImage.src;
            link.download = 'qr_code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const searchInput = document.getElementById('search');
            const entriesSelect = document.getElementById('entries');
            const studentCards = document.getElementById('studentCards');
            const pagination = document.querySelector('.pagination');

            let students = Array.from(studentCards.children);
            let currentPage = 1;
            let entriesPerPage = parseInt(entriesSelect.value);

            function filterCards() {
                const searchTerm = searchInput.value.toLowerCase();
                const filteredStudents = students.filter(student => {
                    return student.querySelector('.card-title').textContent.toLowerCase().includes(searchTerm);
                });
                paginate(filteredStudents);
            }

            function paginate(filteredStudents) {
                const totalStudents = filteredStudents.length;
                const totalPages = Math.ceil(totalStudents / entriesPerPage);
                const start = (currentPage - 1) * entriesPerPage;
                const end = start + entriesPerPage;
                const paginatedStudents = filteredStudents.slice(start, end);

                studentCards.innerHTML = '';
                paginatedStudents.forEach(student => studentCards.appendChild(student));

                pagination.innerHTML = '';
                for (let i = 1; i <= totalPages; i++) {
                    const pageItem = document.createElement('li');
                    pageItem.classList.add('page-item');
                    if (i === currentPage) pageItem.classList.add('active');
                    const pageLink = document.createElement('a');
                    pageLink.classList.add('page-link');
                    pageLink.textContent = i;
                    pageLink.addEventListener('click', function (e) {
                        e.preventDefault();
                        currentPage = i;
                        paginate(filteredStudents);
                    });
                    pageItem.appendChild(pageLink);
                    pagination.appendChild(pageItem);
                }
            }

            entriesSelect.addEventListener('change', function () {
                entriesPerPage = parseInt(this.value);
                currentPage = 1;
                filterCards();
            });

            searchInput.addEventListener('keyup', filterCards);

            filterCards(); // Initialize
        });

    </script>
    <script>
        $(document).ready(function () {
            $("#basic-datatables").DataTable({
                "pageLength": 5, // Default number of entries per page
                "lengthMenu": [5, 10, 25, 50, 75, 100], // Options for the number of entries to show
                "paging": true // Enable pagination
            });
        });
    </script>


    <script>

    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
    // Show success message and hide after 10 seconds
    document.addEventListener('DOMContentLoaded', function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.transition = 'opacity 1s';
            alert.style.opacity = '0';
            setTimeout(function() {
            alert.style.display = 'none';
            }, 1000);
        }, 5000);
        });
    });
    </script>

    <!-- Global Loader Script -->

</body>

</html>