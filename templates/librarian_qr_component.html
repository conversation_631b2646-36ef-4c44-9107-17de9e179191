<head>
    <style>
        body {
            background: linear-gradient(135deg, #1e293b, #3b82f6);
            color: #ffffff;
            font-family: 'Roboto', sans-serif;
            min-height: 100vh;
        }
        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .qr-card {
            margin-top: 1rem;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            max-width: 300px;
            width: 100%;
            background: #ffffff;
            color: #1e293b;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .qr-card h1 {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .qr-card img {
            border: 5px solid #3b82f6;
            border-radius: 10px;
            margin-top: 20px;
            max-width: 100%;
            height: auto;
        }
        .qr-card p {
            font-size: 1rem;
            margin-top: 15px;
            color: #6b7280;
        }
        .download-btn {
            padding: 10px 20px;
            font-size: 1rem;
            background-color: #3b82f6;
            color: #ffffff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .download-btn:hover {
            background-color: #1d4ed8;
        }
        footer {
            position: absolute;
            bottom: 10px;
            width: 100%;
            text-align: center;
            color: #ffffff;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container qr-code-container">
        <div class="qr-card">
            <h1>QR Code for {{ librarian.library_name }}</h1>
            <img id="qrImage" src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid">
            <p>Scan the QR code above to view the details.</p>
            <button class="download-btn" onclick="downloadQR()">Download QR Code</button>
        </div>
    </div>
    <footer>
        &copy; 2024 Library Management System. All Rights Reserved.
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadQR() {
            const qrImage = document.getElementById('qrImage');
            const link = document.createElement('a');
            link.href = qrImage.src;
            link.download = 'qr_code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>

