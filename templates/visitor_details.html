<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Visitors</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }

        /* .form-control {
            border: none;
            outline: none;
            box-shadow: none;
            margin-left: 18px;
        } */

        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }



    @media only screen and (max-width: 767px) {
            .small_p p{
            font-size: 12px !important;
        }
        }
        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }

        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }

        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }
        .container {
            max-width: 800px;
            /* margin-top: 80px; Increased top margin for better spacing */
            margin-bottom: 50px;
            background-color: #ffffff; /* White background */
            padding: 30px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse; /* Remove spacing between table cells */
        }
        table th, table td {
            padding: 10px;
            vertical-align: middle; /* Center align vertically */
            border: 1px solid #dee2e6;
        }
        table tbody tr:hover {
            background-color: #f1f1f1; /* Highlight row on hover */
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05); /* Striped rows */
        }
        .status {
            font-weight: bold;
            text-transform: capitalize;
        }
        .btn-group {
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .status-badge {
            font-size: 1em;
            padding: 0.5em;
            border-radius: 5px;
        }
        .text-center {
            text-align: center;
        }
        .brain img {
            width: 350px;
            height: auto;
            display: flex;
            margin-left: auto;
            margin-right: auto;

            opacity: 0.6;
            /* margin-bottom: 10px; */
        }
        .btn-group {
            margin-top: 10px;
            margin-bottom: 5px;
        }
        @media (max-width: 576px) {
            .container {
                padding: 15px;
                margin-top: 60px; /* Adjusted for smaller screens */
                margin-bottom: 30px;
            }
            table th, table td {
                font-size: 0.9em; /* Smaller font for mobile */
            }
            .brain img {
                width: 60%;
                height: auto;
                margin-left: 25%;
                margin-right: 25%;
                margin-top: 1px;
                opacity: 0.6;
                margin-bottom: 10px;
            }
            .btn-group {
                margin-top: 10px;
                margin-bottom: 5px;
            }
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }

        #notificationBtn {
            display: none;
        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
{% include "sidebar.html" %}

        <div class="container">
            <div class="row">
                <div class="col-md-12 small_p">
                    <h1>{{ visitor.name }} Details</h1>
                        <table class="table table-bordered table-hover">
                            <tbody>
                                <tr>
                                    <th scope="row">Visitor ID</th>
                                    <td>{{ visitor.inqid }}</td>
                                </tr>
                                <tr>
                                    <th scope="row">Name</th>
                                    <td>{{ visitor.name }}</td>
                                </tr>
                                <tr>
                                    <th scope="row">Visit Date</th>
                                    <td>{{ visitor.date }}</td>
                                </tr>
                                <tr>
                                    <th scope="row">Visitor Mobile</th>
                                    <td>
                                        {{ visitor.contact }}
                                        <a href="tel:{{ visitor.contact }}" class="ms-2">
                                            <i class="fas fa-phone-alt"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="copyToClipboard('{{ visitor.contact }}')" class="ms-2">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Visitor Email</th>
                                    <td>
                                        {{ visitor.email }}
                                        <a href="https://mail.google.com/mail/?view=cm&fs=1&to={{ visitor.email }}" target="_blank" class="ms-2">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="copyToClipboard('{{ visitor.email }}')" class="ms-2">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Shift</th>
                                    <td>
                                        {% for shift in visitor.shift.all %}
                                            <span class="badge bg-secondary">{{ shift.name }}</span>
                                            {% if not forloop.last %} | {% endif %}
                                        {% endfor %}
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Note</th>
                                    <td>{{ visitor.notes }}</td>
                                </tr>
                                <tr>
                                    <th scope="row">Callback Date</th>
                                    <td>{{ visitor.callback }}</td>
                                </tr>
                                <tr>
                                    <th scope="row">Status</th>
                                    <td>
                                        <span class="status-badge text-{{ visitor.status }}">
                                            {{ visitor.status }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    <div class="text-center">
                        <a href="/visitors/" class="btn btn-secondary mb-2"><i class="fas fa-arrow-left"></i> Back to Visitor List</a>
                        <a href="/visitors/update/{{visitor.slug}}/" class="btn btn-warning mb-2"><i class="fas fa-edit"></i> Edit</a>
                        <button onclick="confirmDelete()" class=" mb-2 btn btn-danger"><i class="fas fa-trash-alt"></i> Delete</button>
                    </div>
                </div>
            </div>
            </div>
        <div class="footer te">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
        </div>
    </div>

    <script>
        function confirmDelete() {
            // Show confirmation dialog with two options
            if (confirm('Are you sure you want to delete this visitor?')) {
                // Redirect to delete URL
                window.location.href = '/visitors/delete/{{ visitor.slug }}/';
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Optional: Show a success message
                console.log('Copied to clipboard: ' + text);
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
         <script>
            function updateDateTime() {
                const now = new Date();
                const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
                const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

                document.getElementById('date').textContent = 'Date: ' + date;
                document.getElementById('time').textContent = 'Time: ' + time;
            }

            // Update the date and time on page load
            updateDateTime();

            // Update the date and time every minute
            setInterval(updateDateTime, 60000);
        </script>

    <!-- Bootstrap 5 JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>