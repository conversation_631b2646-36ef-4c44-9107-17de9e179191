import json
import random
import string
import razorpay
from django.conf import settings
from django.shortcuts import render, redirect
from django.utils import timezone
from .models import *
from librarian.models import *
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_protect
from wallet_and_transactions.models import *
from django.shortcuts import render
from django.http import JsonResponse
from .models import Coupon
from datetime import datetime
from django.views.decorators.http import require_http_methods
from django.contrib import messages


# Initialize Razorpay client
client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))


@login_required(login_url="/librarian/login/")
def plan_list(request):
    plans = Plan.objects.all().order_by("price")[1:]
    return render(request, "package.html", {"plans": plans})


@login_required(login_url="/librarian/login/")
def sms_plan_list(request):
    sms_plans = SmsPlan.objects.all().order_by("price")
    return render(request, "package.html", {"smsplans": sms_plans})


@login_required(login_url="/librarian/login/")
def pre_package(request, id):
    try:
        plan = Plan.objects.get(id=id)
    except Plan.DoesNotExist:
        print("Plan not found")
        return redirect("plan_list")

    # Create Razorpay Order
    order_amount = int(plan.discount_price * 100)

    context = {
        "amount": order_amount,
        "plan": plan,
    }  # Amount in paise
    return render(request, "pre_payment.html", context)


@login_required(login_url="/librarian/login/")
def create_order(request, id):
    try:
        plan = Plan.objects.get(id=id)
    except Plan.DoesNotExist:
        print("Plan not found")
        return redirect("plan_list")

    num_months_str = request.POST.get("num_months", "1")
    try:
        num_months = float(num_months_str)
    except ValueError:
        num_months = 1.0

    # Calculate order amount in paise (multiplying by 100)
    order_amount1 = float(request.POST.get("grand_total")) * 100  # Amount in paise

    currency = "INR"
    receipt = f"order_rcptid_{int(timezone.now().timestamp())}"

    coupon_code = request.POST.get("coupon_code")
    discount = 0  # Default no discount

    if coupon_code:
        try:
            coupon = Coupon.objects.get(
                code=coupon_code, is_active=True, expiry_date__gte=timezone.now().date()
            )
            # Validate the coupon for the plan duration
            if (
                (plan.duration_months == 1 and coupon.discount == 20)
                or (plan.duration_months == 3 and coupon.discount == 30)
                or (plan.duration_months >= 6 and coupon.discount == 50)
            ):
                discount = coupon.discount
                # Deactivate the coupon after use
                coupon.is_active = False
                coupon.save()
            else:
                messages.error(request, "Coupon is not applicable for this plan.")
                return redirect("checkout")
        except Coupon.DoesNotExist:
            messages.error(request, "Invalid or expired coupon code.")
            return redirect("checkout")

    try:
        order = client.order.create(  # type: ignore
            dict(
                amount=order_amount1,
                currency=currency,
                receipt=receipt,
                payment_capture="1",
            )
        )
    except razorpay.errors.RazorpayError as e:  # type: ignore
        print("Error creating Razorpay order:", e)
        return redirect("failure")

    context = {
        "order_id": order["id"],
        "razorpay_key": settings.RAZORPAY_KEY_ID,
        "amount": order_amount1,
        "currency": currency,
        "receipt": receipt,
        "plan": plan,
        "num_months": num_months,
        "coupon_code": coupon_code,
        "discount": discount,
    }

    print("response",context)

    return render(request, "checkout.html", context)


@login_required(login_url="/librarian/login/")
@csrf_protect  # Ensure CSRF protection
def verify_payment(request):
    if request.method == "POST":
        razorpay_order_id = request.POST.get("razorpay_order_id")
        razorpay_payment_id = request.POST.get("razorpay_payment_id")
        razorpay_signature = request.POST.get("razorpay_signature")
        plan_id = request.POST.get("plan_id")
        # Use GET to retrieve num_months passed in the URL query string
        num_months_str = request.POST.get("num_months", "1")
        try:
            num_months = float(num_months_str)
        except ValueError:
            num_months = 1.0

        if not all(
            [razorpay_order_id, razorpay_payment_id, razorpay_signature, plan_id]
        ):
            print("Missing payment or plan information")
            return redirect("failure")

        try:
            # Ensure the user is authenticated and get the librarian object
            library = Librarian_param.objects.get(user=request.user)
            plan = Plan.objects.get(id=plan_id)
            # Create or update the membership record
            membership = Membership.objects.get(librarian=library)

            current_date = timezone.now().date()
            membership.plan = plan
            membership.start_date = current_date
            membership.expiry_date = current_date + timezone.timedelta(
                days=30 * num_months
            )
            membership.razorpay_order_id = razorpay_order_id
            membership.razorpay_payment_id = razorpay_payment_id
            membership.razorpay_signature = razorpay_signature

            membership.save()

            # Create a transaction for the librarian's wallet
            user_wallet = Wallet.objects.get(user=request.user)
            amount = plan.sms_quantity * num_months

            note = f"Librarian selected plan {plan} and received {amount} points"

            transaction = Transaction.objects.create(
                wallet=user_wallet, amount=amount, is_credit=True, note=note
            )
            user_wallet.balance = transaction.amount
            user_wallet.save()

            return redirect("success")

        except Plan.DoesNotExist:
            print("Invalid plan ID")
            return redirect("failure")
        except razorpay.errors.SignatureVerificationError as e:  # type: ignore
            print("Payment verification failed:", e)
            return redirect("failure")
        except Exception as e:
            print("Error during payment verification:", str(e))
            return redirect("failure")
    else:
        return redirect("plan_list")


@login_required(login_url="/librarian/login/")
def create_sms_order(request, id):
    try:
        plan = SmsPlan.objects.get(id=id)
    except SmsPlan.DoesNotExist:
        print("SMS Plan not found")
        return redirect("sms_plan_list")

    # Create Razorpay Order
    order_amount = float(plan.price * 100)  # Amount in paise
    currency = "INR"
    receipt = f"order_rcptid_{int(timezone.now().timestamp())}"

    try:
        order = client.order.create(  # type: ignore
            dict(
                amount=order_amount,
                currency=currency,
                receipt=receipt,
                payment_capture="1",
            )
        )
    except razorpay.errors.RazorpayError as e:  # type: ignore
        print("Error creating Razorpay order:", e)
        return redirect("failure")

    context = {
        "order_id": order["id"],
        "razorpay_key": settings.RAZORPAY_KEY_ID,
        "amount": order_amount,
        "currency": currency,
        "receipt": receipt,
        "plan": plan,
    }
    return render(request, "sms_checkout.html", context)


@login_required(login_url="/librarian/login/")
@csrf_protect  # Ensure CSRF protection
def sms_verify_payment(request):
    if request.method == "POST":
        razorpay_order_id = request.POST.get("razorpay_order_id")
        razorpay_payment_id = request.POST.get("razorpay_payment_id")
        razorpay_signature = request.POST.get("razorpay_signature")
        smsplan_id = request.POST.get("plan_id")

        if not all(
            [razorpay_order_id, razorpay_payment_id, razorpay_signature, smsplan_id]
        ):
            print("Missing payment or sms plan information")
            return redirect("failure")

        try:
            # Ensure the user is authenticated and get the librarian object
            user = request.user
            plan = SmsPlan.objects.get(id=smsplan_id)

            user_wallet = Wallet.objects.get(user=user)

            user_wallet.razorpay_order_id = razorpay_order_id
            user_wallet.razorpay_payment_id = razorpay_payment_id
            user_wallet.razorpay_signature = razorpay_signature

            amount = plan.sms_quantity
            note = f"Librarian select sms plan {plan} and give {amount} points"

            transaction = Transaction.objects.create(
                wallet=user_wallet, amount=amount, is_credit=True, note=note
            )
            user_wallet.balance = transaction.amount
            user_wallet.save()

            return redirect("success")

        except SmsPlan.DoesNotExist:
            print("Invalid SMS plan ID")
            return redirect("failure")
        except razorpay.errors.SignatureVerificationError as e:  # type: ignore
            print("Payment verification failed:", e)
            return redirect("failure")
        except Exception as e:
            print("Error during payment verification:", str(e))
            return redirect("failure")
    else:
        return redirect("plan_list")


@login_required(login_url="/librarian/login/")
def success(request):
    return render(request, "success.html")


@csrf_protect
@require_http_methods(["GET", "POST"])
def admin_generate_coupons(request):
    if request.method == "POST":
        try:
            discount = int(request.POST.get("discount"))
            count = int(request.POST.get("count"))
            expiry_date = request.POST.get("expiry_date")

            if count < 1 or count > 1000:
                return JsonResponse(
                    {
                        "success": False,
                        "error": "Number of coupons must be between 1 and 1000",
                    },
                    status=400,
                )

            expiry_date_obj = datetime.strptime(expiry_date, "%Y-%m-%d")

            generated_coupons = []
            for _ in range(count):

                while True:
                    code = "".join(
                        random.choices(string.ascii_uppercase + string.digits, k=10)
                    )

                    if not Coupon.objects.filter(code=code).exists():
                        break

                coupon = Coupon.objects.create(
                    code=code, discount=discount, expiry_date=expiry_date_obj
                )
                generated_coupons.append(coupon.code)

            return JsonResponse(
                {"success": True, "generated_coupons": generated_coupons}
            )

        except ValueError:

            return JsonResponse(
                {"success": False, "error": "Invalid input values"}, status=400
            )

        except Exception as e:

            return JsonResponse({"success": False, "error": str(e)}, status=500)

    generated_coupons = Coupon.objects.all()
    return render(request, "create_coupons.html", {"all_coupons": generated_coupons})


def validate_coupon(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            coupon_code = data.get("coupon_code", "")

            if not coupon_code:
                return JsonResponse(
                    {"valid": False, "message": "Coupon code is required."}
                )

            valid_coupons = Coupon.objects.filter(code=coupon_code, is_active=True)

            if valid_coupons.exists():
                return JsonResponse(
                    {"valid": True, "message": "Coupon applied successfully!", "discount": valid_coupons[0].discount, "discount_type": valid_coupons[0].discount_type} )
            else:
                return JsonResponse({"valid": False, "message": "Invalid coupon code."})

        except json.JSONDecodeError:
            return JsonResponse(
                {"valid": False, "message": "Invalid JSON format in request."}
            )
        except Exception as e:
            return JsonResponse(
                {"valid": False, "message": f"An error occurred: {str(e)}"}
            )

    return JsonResponse({"valid": False, "message": "Invalid request method."})


@login_required(login_url="/librarian/login/")
def failure(request):
    return render(request, "failure.html")
