<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Coupon</title>
    <!-- Bootstrap CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    

    <style>
        body {
            -webkit-user-select: none;
            user-select: none;
            margin: 16px;
            font-family: 'Comfortaa', sans-serif !important;
            background-color: #9bc6bf;
        }

        .btn_theme {
            padding: 8px 16px;
            color: #fff;
            background-color: #294282;
            border: none;
        }

        .btn_theme:hover {
            color: #fff;
            background-color: #28345a;
        }

        .form-control{
            border-radius: 1rem;
        }

        .required-label::after {
        content: " *";
        color: red;
        font-size: 14px;
        position: relative;
        top: -3px;
        }
    </style>
</head>
<body>
    {% include "sidebar.html" %}  
    <div class="container d-flex justify-content-center align-items-center vh-100">
        <div class="col-md-6 col-12">
            <h2 class="p-3 text-white text-center" style="background-color: #294282; border-radius: 1rem;">
            {%if update%}  
                Update Coupon
            {%else%}  
                Create Coupon
            {%endif%}
            </h2>
            
            {%if update%}
                <div class="card shadow p-4 rounded-4">
                    <form id="couponForm" method="POST" action="/blogs/coupons/coupon_detail_view/{{coupon.id}}" novalidate>
                        <!-- Discount Amount -->
                        {% csrf_token %} 
                        <div class="mb-3" id="discount_value_div">
                            <label for="discount_value" class="form-label required-label">
                                <i class="fas fa-tag"></i> Coupon Code
                            </label>
                            <input type="text" class="form-control" id="coupon_code" value="{{coupon.code}}" name="coupon_code" placeholder="Enter coupon code"  required>
                        </div>
                        <div class="mb-3">
                            <label for="discount_type" class="form-label required-label">
                                Discount Type
                            </label>
                            <select  class="form-select" value="{{coupon.discount_type}}" id="discount_type" name="discount_type" style="border-radius: 1rem;" required>
                                <option value="" disabled >Select discount type</option>
                                <option value="percentage">Percentage</option>
                                <option value="amount">Amount</option>
                            </select>
                        </div>
                        
                        <!-- Discount Amount -->
                        <div class="mb-3" id="discount_value_div" >
                            <label for="discount_value" class="form-label required-label">
                                <i class="fas fa-tag"></i> Discount Value
                            </label>
                            <input type="number" class="form-control" id="discount_value" value={{coupon.discount}} name="discount_value" placeholder="Enter discount amount" min="0" required>
                            <div class="invalid-feedback">Please enter a valid discount value.</div>
                        </div>

                        <!-- Expiration Date -->
                        <div class="mb-3">
                            <label for="expirationDate" class="form-label required-label">
                                <i class="fas fa-calendar-alt"></i> Expiration Date
                            </label>
                            <input type="date" class="form-control" id="expirationDate" name="expiryDate"  value="{{ coupon.expiry_date|date:'Y-m-d' }}"  required>
                            <div class="invalid-feedback">Please select a valid expiration date.</div>
                        </div>

                        <!-- Usage Limit -->
                        <div class="mb-3">
                            <label for="usageLimit" class="form-label ">
                                <i class="fas fa-users"></i> Usage Limit
                            </label>
                            <input type="number" class="form-control" id="usageLimit" name="usageLimit" placeholder="Enter usage limit" value={{ coupon.usage_limit }} min="1">
                            <div class="invalid-feedback">Usage limit must be at least 1.</div>
                        </div>

                        <!-- Overall Discount Amount -->
                        <div class="mb-3">
                            <label for="overallDiscountAmount" class="form-label">
                                <i class="fas fa-money-bill-wave"></i> Overall Discount Amount
                            </label>
                            <input type="number" class="form-control" id="overallDiscountAmount" name="overallDiscountAmount" placeholder="Enter overall discount amount" value={{ coupon.overallDiscountAmount }} min="0" >
                            <div class="invalid-feedback">Please enter a valid overall discount amount.</div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="btn btn_theme w-100">
                                <i class="fas fa-plus-circle"></i> Update Coupon
                            </button>
                        </div>
                    </form>
                </div>
            {%else%}
            <div class="card shadow p-4 rounded-4">
                <form id="couponForm" method="POST" novalidate>
                    {% csrf_token %} 
                    <div class="mb-3" id="coupon_code_div">
                        <label for="coupon_code" class="form-label required-label">
                            <i class="fas fa-tag"></i> Coupon Code
                        </label>
                        <input type="text" class="form-control" id="coupon_code" name="coupon_code" placeholder="Enter coupon code"  required>
                    </div>
                    <div class="mb-3">
                        <label for="discount_type" class="form-label required-label">
                            Discount Type
                        </label>
                        <select class="form-select" id="discount_type" name="discount_type" style="border-radius: 1rem;" required>
                            <option value="" disabled selected>Select discount type</option>
                            <option value="percentage">Percentage</option>
                            <option value="amount">Amount</option>
                        </select>
                    </div>
                    
                    <!-- Discount Amount -->
                    <div class="mb-3" id="discount_value_div" style="display: none;">
                        <label for="discount_value" class="form-label required-label">
                            <i class="fas fa-tag"></i> Discount Value
                        </label>
                        <input type="number" class="form-control" id="discount_value" name="discount_value" placeholder="Enter discount amount" min="0" required>
                        <div class="invalid-feedback">Please enter a valid discount value.</div>
                    </div>

                    <!-- Expiration Date -->
                    <div class="mb-3">
                        <label for="expirationDate" class="form-label required-label">
                            <i class="fas fa-calendar-alt"></i> Expiration Date
                        </label>
                        <input type="date" class="form-control" id="expirationDate" name="expiryDate" required>
                        <div class="invalid-feedback">Please select a valid expiration date.</div>
                    </div>

                    <!-- Usage Limit -->
                    <div class="mb-3">
                        <label for="usageLimit" class="form-label">
                            <i class="fas fa-users"></i> Usage Limit
                        </label>
                        <input type="number" class="form-control" id="usageLimit" name="usageLimit" placeholder="Enter usage limit" min="1" >
                        <div class="invalid-feedback">Usage limit must be at least 1.</div>
                    </div>

                    <!-- Overall Discount Amount -->
                    <div class="mb-3">
                        <label for="overallDiscountAmount" class="form-label">
                            <i class="fas fa-money-bill-wave"></i> Overall Discount Amount
                        </label>
                        <input type="number" class="form-control" id="overallDiscountAmount" name="overallDiscountAmount" placeholder="Enter overall discount amount" min="0" >
                        <div class="invalid-feedback">Please enter a valid overall discount amount.</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn_theme w-100">
                            <i class="fas fa-plus-circle"></i> Create Coupon
                        </button>
                    </div>
                </form>
            </div>
            {%endif%}
        </div>
    </div>



    <script>
        const discountTypeSelect = document.getElementById('discount_type');
        const discountValueDiv = document.getElementById('discount_value_div');
        const discountValueInput = document.getElementById('discount_value');
        
        discountTypeSelect.addEventListener('change', function() {
            const selectedValue = discountTypeSelect.value;
            discountValueInput.value = 0;
            
            if (selectedValue) {
                discountValueDiv.style.display = 'block'; // Show the discount value input field
                
                // Set max value to 100 if "Percentage" is selected
                if (selectedValue === 'percentage') {
                    discountValueInput.max = 100;
                } else {
                    discountValueInput.removeAttribute('max'); // Remove max limit for "Amount"
                }
            } else {
                discountValueDiv.style.display = 'none'; // Hide the discount value input field if no selection
            }
        });
    </script>
    <script>
        // Disable past dates for expiration date
        document.getElementById('expirationDate').setAttribute('min', new Date().toISOString().split('T')[0]);

        // Bootstrap validation and prevent negative values
        document.getElementById('couponForm').addEventListener('submit', function(event) {
            let form = this;
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });

        // Prevent negative values in number inputs
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function () {
                if (this.value < 0) this.value = 0;
            });
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
