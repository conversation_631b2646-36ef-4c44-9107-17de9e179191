<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Backups</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">


  
        
        <!-- disable Print Screen for Windows -->
    
          
        
        <!-- disable print screen for mac -->
    
          
    
        <!-- disabling print screen for Linux -->
    
  
    
        <!-- disabling inspection tool -->
    
          
    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            background-color: #f5f6f8;
            color: #2c3e50;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        .container {
            max-width: 900px;
            margin-top: 3rem;
            position: relative;
        }

        .back-button {
            position: absolute;
            left: 0;
            top: 0;
            color: #3498db;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04);
        }

        .back-button:hover {
            color: #2980b9;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
            transform: translateY(-1px);
        }

        .page-header {
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title {
            font-weight: 300;
            color: #34495e;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1rem;
            margin-bottom: 2rem;
        }

        .list-group-item {
            border: none;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
            transform: translateY(-1px);
        }

        .file-name {
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .file-info {
            font-size: 0.875rem;
            color: #95a5a6;
        }

        .btn-download {
            background-color: #3498db;
            border: none;
            padding: 0.5rem 1.25rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            color: white;
        }

        .btn-download:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
            color: white;
        }

        .alert-no-files {
            background-color: white;
            border: 1px solid #eee;
            color: #7f8c8d;
            padding: 2rem;
            text-align: center;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04);
        }

        .icon-download {
            margin-left: 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>    

    <div class="container">
        <a href="/librarycommander/dashboard/" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Back to Dashboard
        </a>

        <div class="page-header text-center">
            <h1 class="page-title">Database Backups</h1>
            <p class="subtitle">Access and download your database backup files</p>
        </div>

        <div class="list-group">
            {% if files %}
                {% for file in files %}
                    <div class="list-group-item d-flex justify-content-between align-items-center p-3">
                        <div>
                            <div class="file-name">{{ file }}</div>
                            <div class="file-info">Created on {% now "F j, Y" %}</div>
                        </div>
                        <a href="{% url 'download_backup' file %}" class="btn btn-download">
                            Download
                            <i class="fas fa-download icon-download"></i>
                        </a>
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert-no-files">
                    <p class="mb-0">No backup files are currently available.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>