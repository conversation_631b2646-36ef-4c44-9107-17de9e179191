<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- fav icon -->
    <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
    


    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>

        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #cee3e0 !important;
        }

        .card-primary {
            background-color: #1572E8;
            color: white;
            border-radius: 15px;
        }

        .card-info {
            background-color: #48ABF7;
            color: white;
            border-radius: 15px;
        }

        .card-success {
            background-color: #31CE36;
            color: white;
            border-radius: 15px;
        }

        .card-secondary {
            background-color: #6861CE;
            color: white;
            border-radius: 15px;
        }

        .icon-big {
            font-size: 3rem;
        }

        .card-round {
            border-radius: 15px;
        }


        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            /* Full height of the viewport */
            overflow-y: auto;
            /* Enable vertical scrolling */
            overflow-x: hidden;
            /* Hide horizontal overflow */

            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar h4 {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #495057;
        }

        .sidebar a {
            color: #fff;
            padding: 15px 10px;
            padding-left: 25px;
            display: block;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .sidebar a:hover,
        .sidebar a.active {
            background-color: #495057;
            border-left: 3px solid #007bff;

        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #343a40;
            color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        .dropdown-content a {
            color: white;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

        .show {
            display: block;
        }


        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        /* .navbar {
            transition: all 0.3s;
            margin-bottom: 20px;
        } */
        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150,142,150,1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }



        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }

        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }

        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }

        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /* 
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 50px;
            }

            .footer img {
                width: 60%;
                padding-bottom: 60px;
            }

        }

        #notificationBtn {
            display: none;
        }
        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        @media only screen and (max-width: 767px) {
            .small_p p{
            font-size: 12px !important;
        }
        }

        .analitics_container p {
            font-size: 1rem;
        }

        .analitics_container i {
            font-size: 1.9rem;
        }

        .col-stats {
            padding: 0;
            margin: 0;
        }

    </style>
</head>

<body>


    {% include "sidebar.html" %}

    <br>
        <div class="d-flex justify-content-center">
            <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
               DASHBOARD / ANALYTICS
            </div>
        </div>

    <div class="container-fluid analitics_container">
        
        <h3 class="fw-bold mb-3">Student & Visitor Data</h3>
        <div class="row">
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Student</p>
                                    <h4 class="card-title">{{total_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-info card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Male</p>
                                    <h4 class="card-title">{{male_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-success card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person-dress"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Female</p>
                                    <h4 class="card-title">{{female_student_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3 mb-3">
                <div class="card card-stats card-secondary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Students ({% now "F Y" %}) </p>
                                    <h4 class="card-title"> {{monthly_counts}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">{{total_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-clock"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Pending Visitors</p>
                                    <h4 class="card-title">{{pending_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-person-dress"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Completed Visitors</p>
                                    <h4 class="card-title">{{completed_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <!-- <div class="col-sm-6 col-md-3 mt-3">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Cancled Visitors</p>
                                    <h4 class="card-title">{{cancelled_visitors_data}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <div class="container-fluid mt-5 mb-4 analitics_container">
        <h3 class="fw-bold mb-3">Financial Analytics Data</h3>
        <div class="row">
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-coins"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Y'Day Closing Balance</p>
                                    <h4 class="card-title">{{yesterday_closing_balance}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-success text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-indian-rupee-sign"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Daily Collection</p>
                                    <h4 class="card-title">{{daily_total_balance}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <!-- <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-alarm-fill"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Due Collectable</p>
                                    <h4 class="card-title">{{total_amount_due}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
            {% comment %} <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-secondary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-cash-coin"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Monthly Total Collection</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Monthly Profit & loss</p>
                                    <h4 class="card-title">1,294</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-warning text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-clock"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Extra</p>
                                    <h4 class="card-title">100</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-success text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-chart-pie"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Quarterly Total Collection</p>
                                    <h4 class="card-title">100</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-primary card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Quarterly Profit & Loss</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats card-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fa-solid fa-chart-pie"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Half Yr. Total Collection</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-danger text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Half Yr. Profit & Loss</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-4 mb-4">
                <div class="card card-stats bg-secondary text-white card-round">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-3">
                                <div class="icon-big text-center">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="col-9 col-stats">
                                <div class="numbers">
                                    <p class="card-category">Total Visitor</p>
                                    <h4 class="card-title">123</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> {% endcomment %}
        </div>
        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">
            <!-- <p>Developed with passion by Librainian</p> -->
        </div>
    </div>





    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        document.getElementById("userDropdown").addEventListener("click", function () {
            document.getElementById("userDropdownContent").classList.toggle("show");
        });

        // Close the dropdown if the user clicks outside of it
        window.addEventListener("click", function (event) {
            if (!event.target.matches("#userDropdown")) {
                var dropdowns = document.getElementsByClassName("dropdown-content");
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains("show")) {
                        openDropdown.classList.remove("show");
                    }
                }
            }
        });

    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>

</body>

</html>