@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Itim&display=swap');



/* ============ Nav bar Section ============== */

html{
    scroll-behavior: smooth;
}


body{
    font-family: "Inter", sans-serif;
}

#mynav{
    position: fixed;
    top: 0%;
    width: 100%;
    z-index: 1000;
    background-color: red;
}

.header{
    display: flex;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}


.main-logo{
    display: block;
    width: 100px;
    height: 100px;
}

.logo .name{
    font-size: 40px;
    font-weight: 900;
}

.link-wrapper .dropdown-toggle{
    font-size: 20px;
    /* margin-left: 20px; */
}



.link-wrapper .links{
    position: relative;
    text-decoration: none;
    color: black;
    font-size: 20px;
    letter-spacing: 0.5px;
    padding: 0 10px;
    margin: 0 5px;
}

.link-wrapper .links-2{
    position: relative;
    text-decoration: none;
    color: white;
    font-size: 22px;
    letter-spacing: 0.5px;
    padding: 0 10px;
    margin: 0 10px;
}



.link-wrapper .links:after{
    content: "";
    position: absolute;
    /* background-color: #353a40; */
    height: 3px;
    width: 0;
    left: 0;
    bottom: -6px;
    transition: 0.3s;
}
.link-wrapper .links:hover:after{
    width: 92%;
}


@media(max-width: 984px){
    .link-wrapper .links{
        margin-left: 50px;
    }
    .link-wrapper .dropdown-toggle{
        margin-left: 50px;
    }
}

/* ============ All Section ============== */

main > section, main > div > section {
    padding-top: 80px;
}



#scrollUpBtn {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 3%;
    z-index: 100;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    background-color: transparent;
}

.strike{
    text-decoration: line-through;
}

#home{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
}
#about{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
}
#services{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
}
#testimonials{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    padding-top: 50px;
}
#team{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    /* padding-top: 40px; */
}
#pricing{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    padding-bottom: 60px;
}
#download{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    /* padding-top: 90px; */
}
#faq{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    padding-top: 40px;
}
#contact{
    /* min-height: 100vh; */
    height: fit-content;
    background-color: #efefef;
    padding-top: 40px;
}




@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section {
    opacity: 0;
    animation: fadeIn 1s ease-out forwards;
}


#about .main-text, #team .main-text, #services .main-text, #about .main-text, #testimonials .main-text, #pricing .main-text, #faq .main-text, #download .main-text, #contact .main-text{
    font-size: 48px;
    text-align: center;
    font-weight: bold;
    font-family: "Itim", cursive;
    margin-top: 30px;
}

#services .main-text{
    margin-bottom: 80px;
}


/* ============ Home Section ============== */


#head{
    margin-top: 0px;
}

#home .box{
    max-width: 80%;
    margin: 0 auto;
}

#home .box .content{
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.home-text h1{
    font-size: 50px;
    font-weight: 900;
    color: black;
}

.home-text h1.diff{
    font-family: "Itim", cursive;
}

@media(max-width: 1440px) {
    #home .box{
        max-width: 100%;
        margin: 0 auto;
    }
    #home .box .content{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background-image: url('/static/img/figma_files/avataar.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        height: 100vh;
        opacity: 0.6;
    }
    .home-text h1{
        font-size: 40px;
        text-align: unset;
    }
    .content div img{
        display: none;
    }
}


/* ============ About Section ============== */


.about-us {
    text-align: center;
    padding: 10px 0;
}

h1 {
    font-size: 2.5rem;
    font-weight: bold;
}

p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #333;
    line-height: 1.6;
    font-weight: 700;
}

.contents {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 40px;
}

.contents-left, .contents-right {
    width: 30%;
    text-align: left;
}

.center-image {
    width: auto;
}

.icon-text {
    margin-top: 10px;
    text-align: center;
}

.icon {
    width: 70px;
    height: auto;
}

@media (min-width: 1200px) {
    .contents {
        max-width: 1200px;
        margin: 0 auto;
        flex-direction: column;
    }

    .contents-left, .contents-right {
        width: 80%;
        gap: 40px;
        display: flex;
        justify-content: space-evenly;
    }


    h1 {
        font-size: 3rem;
        margin-bottom: 40px;
    }

    p {
        font-size: 1.3rem;
    }

    /* .center-image {
        width: 350px;
    } */
}

/* Responsive Design for Tablets (768px - 1200px) */
@media (max-width: 1200px) and (min-width: 768px) {
    .contents {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .contents-left, .contents-right {
        width: 40%;
        text-align: center;
    }

    .center-image {
        margin-top: 30px;
        width: 300px;
    }


    .contents-left p, .contents-right p {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .contents {
        flex-direction: column;
        align-items: center;
    }

    .contents-left, .contents-right {
        width: 90%;
        text-align: center;
    }

    .center-image {
        margin-top: 30px;
        width: 250px;
    }


    .contents-left p, .contents-right p {
        font-size: 1.1rem;
    }

    .icon {
        width: 30px;
    }
}





/* ============ Services Section ============== */


.wrapper {
    /* max-width: 1100px; */
    width: 80%;
    margin: 0 auto;
    margin-top: 40px;
    /* margin-bottom: 40px; */
    position: relative;

}

.wrapper i {
    /* Margin added  */
    /* margin: 0rem -3rem;  */
    height: 50px;
    width: 50px;
    background: #fff;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    cursor: pointer;
    position: absolute;
    z-index: 1000;
    top: 50%;
    font-size: 1.25 rem;
    transform: translateY(-50%);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.23);

}

.wrapper i:first-child {
    left: -64px;

}

.wrapper i:last-child {
    right: -64px;

}



.wrapper .carousel {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: calc((100% / 3) - 12px);
    gap: 40px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    scrollbar-width: 0;
    padding-bottom: 80px;
    margin-bottom: 0;
}

.carousel::-webkit-scrollbar {
    display: none;
}

.carousel :where(.card, .img) {
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel.dragging {
    scroll-snap-type: none;
    scroll-behavior: auto;
}

.carousel.no-transition {
    scroll-behavior: auto;
}

.carousel.dragging .card {
    cursor: grab;
    user-select: none;
}

.carousel .card {
    scroll-snap-align: start;
    height: 550px;
    list-style: none;
    background: #C9E8E0;
    border-top-left-radius: 50px;
    border-bottom-right-radius: 50px;
    display: flex;
    cursor: pointer;
    width: 105%;
    padding-bottom: 15px;
    padding-top: 15px;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.23);
    margin-bottom: 20px;
    box-sizing: border-box;
}

.card .img {
    /* background: green; */
    width: 150px;
    height: 150px;
    margin-bottom: 30px;
    margin-top: 7px;
    /* border-radius: 50%; */

}

.card .img img {
    width: 200px;
    height: 200px;
    object-fit: cover;
}

.card h2 {
    font-weight: 500;
    font-size: 1.56rem;
    margin: 30px 0 5px;
}

.card span {
    color: #6a6d78;
    font-size: 1.31rem;

}

@media screen and (max-width: 900px) {
    .wrapper .carousel {
        grid-auto-columns: calc((100% / 2) - 9px);
    }
    .carousel .card {
        height: 450px;
    }
}

@media screen and (max-width: 600px) {
    .wrapper .carousel {
        grid-auto-columns: 100%;
    }
}

/* ============ Team Section ============== */

.team-box{
    width: 80%;
    margin: auto;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    align-items: center;
    gap: 20px;
}

.team-box .profile-box .img-cont{
    width: 300px;
    height: 300px;
    margin: 10px auto;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 5px 5px 20px grey;
}

.team-box .profile-box img{
    width: 300px;
    height: 300px;
}

.team-box .profile-box .name{
    text-align: center;
    margin-top: 20px;
    font-size: 25px;
    font-weight: 900;
}
.team-box .profile-box .desig{
    text-align: center;
    background-color: #F5C002;
    font-weight: 600;
    padding: 10px;
    width: 50%;
    margin: 10px auto;
    border-radius: 20px;
}
.team-box .profile-box .socials{
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
    cursor: pointer;
}
.team-box .profile-box .socials .fa-twitter{
    color: #1DA1F2;
}

.team-box .profile-box .socials .fa-linkedin{
    color: #0077B5;
}
.team-box .profile-box .socials .fa-instagram{
    color: #E1306C;
}
.team-box .profile-box .socials .fa-facebook{
    color: #1877F2;
}

@media(max-width: 1440px) {
    .team-box{
        grid-template-columns: repeat(2, 1fr);
    }
    .team-box .profile-box .desig{
        width: 50%;
        margin: 10px auto;
    }
}

@media(max-width: 1070px) {
    .team-box{
        grid-template-columns: 1fr;
    }
    .team-box .profile-box .desig{
        width: fit-content;
        margin: 10px auto;
    }
}


/* ============ Pricing Section ============== */

.pricing .box {
    padding: 20px;
    background: #fff;
    text-align: center;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    border: 1px solid #fff;
    width: 90%;
    height: 31rem;
    margin: auto;
}

.pricing .box h3 {
    font-weight: 400;
    padding: 15px;
    margin-top: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #151515;
}

.pricing .box h4 {
    font-size: 42px;
    color: #151515;
    font-weight: 700;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 20px;
}

.pricing .box h4 sup {
    font-size: 20px;
    top: -15px;
    left: -3px;
}

.pricing .box h4 span {
    color: #bababa;
    font-size: 16px;
    font-weight: 300;
}

.pricing .box ul {
    padding: 0;
    list-style: none;
    color: #151515;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
}

.pricing .box ul li {
    padding-bottom: 16px;
}

.pricing .box ul i {
    color: #7cc576;
    font-size: 18px;
    padding-right: 4px;
}

.pricing .box ul .na {
    color: #ccc;
    text-decoration: line-through;
}

.pricing .box .btn-wrap {
    padding: 15px;
    text-align: center;
}

.pricing .box .btn-buy {
    display: inline-block;
    padding: 10px 40px 12px 40px;
    border-radius: 5px;
    border: 2px solid #7cc576;
    color: #7cc576;
    font-size: 14px;
    font-weight: 400;
    font-family: "Montserrat", sans-serif;
    font-weight: 600;
    transition: 0.3s;
}

.pricing .box {
    background: #7cc576;
    color: #fff;
}

.pricing .recommended {
    border-color: #7cc576;
    background: #7cc576;
    color: #fff;
}

.pricing .recommended:hover {
    background: #61b959;
    border-color: #61b959;
}

.pricing .recommended-badge {
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    font-size: 12px;
    padding: 4px 25px 6px 25px;
    background: #eaf6e9;
    color: #7cc576;
    border-radius: 50px;
    text-transform: uppercase;
    font-weight: 600;
}




/* ============ Download Section ============== */

.play-1{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.image-container {
    position: relative;
    display: inline-block;
}

/* Image style */
.image-container img {
    max-width: 100%;
    height: auto;
}

/* Hotspot style */
.hotspot {
    position: absolute;
    top: 60%;  /* Adjust these values */
    left: 17%; /* Adjust these values */
    width: 200px;
    height: 50px;
    /* background-color: rgba(255, 0, 0, 0.5); Semi-transparent red */
    /* border-radius: 50%; */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.down-cont{
    margin-top: 50px;
}

#download .alter-text{
    display: none;
}

.down-sec{
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding-bottom: 40px;
    gap: 100px;
    max-width: 1100px;
    margin: auto;
}

.down-text{
    width: 500px;
    word-wrap: break-word;
    font-size: 25px;
    margin: auto;
    margin-left: 0;
    margin-top: 20px;
    margin-bottom: 20px;
}



@media(max-width: 1200px){
    #download .alter-text{
        font-family: 'Itim', cursive;
        font-size: 48px;
        display: block;
        text-align: center;
    }
    #download .main-text{
        display: none;
    }
    .down-sec{
        display: block;
        width: 100%;
        margin: auto;
    }
    .play-store{
        display: none;
    }
    .down-text{
        width: 80%;
        margin: auto;
    }
    .play-2{
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}



/* ============ Testimonials Section ============== */


.testimonials-section {
    text-align: center;
    padding: 50px 0;
    width: 80%;
    margin: 0 auto;
}


.testimonials-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 20px;
}

.testimonial-card {
    border: 1px solid #ddd;
    border-radius: 10px;
    width: 45%; /* Ensures two cards per row */
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Apply background colors to each testimonial card */
.testimonial-card:nth-child(1) {
    background-color: #F6F6FE;
}

.testimonial-card:nth-child(2) {
    background-color: #F3FFFF;
}

.testimonial-card:nth-child(3) {
    background-color: #FFF7F2;
}

.testimonial-card:nth-child(4) {
    background-color: #ffedb8;
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.testimonial-header img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 50%;
}

.testimonial-header h3 {
    font-size: 1.2rem;
    font-weight: bold;
}

.testimonial-header p {
    font-size: 0.9rem;
    color: #666;
}

.testimonial-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
}


/* Responsive Design for Tablets and Mobiles */
@media (max-width: 1200px) {
    .testimonial-card {
        width: 90%; /* Single column on smaller screens */
    }
}

@media (max-width: 768px) {
    .testimonial-card {
        width: 100%;
        margin-bottom: 20px;
    }

    .testimonial-header h3 {
        font-size: 1.1rem;
    }

    .testimonial-header p {
        font-size: 0.85rem;
    }

    #pricing {
        padding-bottom: 20px !important;
    }

    .wrapper .carousel {
        margin-bottom: 0px !important;
        padding-bottom: 10px !important;
    }
}


/* ============ Contact Section ============== */

.sec8{
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding-bottom: 80px;
    gap: 100px;
    max-width: 1100px;
    margin: auto;
}

.sec8 .tag{
    color: #91ac41;
    font-size: 24px;
    margin: 0;
    margin-top: 35px;
}

.sec8 .head{
    font-family: "Itim", cursive;
    font-size: 44px;
    font-weight: 600;
    margin-bottom: 20px;
}

.sec8 .desc{
    font-family: "Itim", cursive;
    font-size: 16px;
    font-weight: 500;
    color: #8b8b99;
    width: 400px;
    word-wrap: break-word;
    letter-spacing: 0.5px;
    margin-bottom: 40px;
}

.add1 , .add2{
    margin-bottom: 40px;
}

.add .des{
    font-family: "Itim", cursive;
    font-size: 16px;
    font-weight: 500;
    color: #8b8b99;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

.sec8 .touch{
    padding: auto 60px;
}

.touch input{
    display: block;
    padding: 20px;
    width: 100%;
    margin-bottom: 20px;
    font-size: 16px;
    border: 1px solid #d6d6e3;
    outline: none;
}

.touch textarea{
    display: block;
    padding: 20px;
    width: 100%;
    margin-bottom: 20px;
    font-size: 16px;
    border: 1px solid #d6d6e3;
    outline: none;
}

.touch .submit-btn{
    font-size: 16px;
    font-weight: 600;
    color: white;
    background-color: rgb(170, 210, 54);
    padding: 14px 34px;
    border-radius: 4px;
    border: none;
    margin: 10px auto;
}

.touch .submit-btn:hover{
    background-color: rgb(153, 193, 34);
}

@media(max-width: 1100px){
    .sec8{
        display: block;
        width: 60%;
        margin: 10px auto;
    }
    .sec8-2{
        text-align: center;
    }
    .sec8 .add p.desc{
        width: 80%;
        margin: 10px auto;
    }
    .touch input, .touch textarea{
        display: block;
        padding: 20px;
        width: 100%;
        margin-bottom: 20px;
        font-size: 16px;
        border: 1px solid #d6d6e3;
        outline: none;
        margin: 10px auto;
    }
}


/* ============ Footer Section ============== */


#footer {
    background: url("../img/footer-bg.jpg") center center no-repeat;
    color: #fff;
    font-size: 14px;
    position: relative;
}

#footer::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    z-index: 1;
}

#footer .footer-top {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 80px 0;
}



#footer .footer-top .footer-logo img {
    height: 80px;
}

#footer .footer-top h3 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    position: relative;
    font-family: "Poppins", sans-serif;
    padding: 30px 0 0 0;
    margin-bottom: 0;
}

#footer .footer-top p {
    font-size: 15;
    font-style: italic;
    margin: 30px 0 0 0;
    padding: 0;
}

#footer .footer-top .footer-newsletter {
    text-align: center;
    font-size: 15px;
    margin-top: 30px;
}

#footer .footer-top .footer-newsletter form {
    background: #fff;
    padding: 6px 10px;
    position: relative;
    border-radius: 50px;
    box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
    text-align: left;
}

#footer .footer-top .footer-newsletter form input[type="email"] {
    border: 0;
    padding: 4px 8px;
    width: calc(100% - 100px);
}

#footer .footer-top .footer-newsletter form input[type="submit"] {
    position: absolute;
    top: 0;
    right: -1px;
    bottom: 0;
    border: 0;
    background: none;
    font-size: 16px;
    padding: 0 20px;
    background: #7cc576;
    color: #fff;
    transition: 0.3s;
    border-radius: 50px;
    box-shadow: 0px 2px 15px rgba(0, 0, 0, 0.1);
}

#footer .footer-top .footer-newsletter form input[type="submit"]:hover {
    background: #5ab652;
}

#footer .footer-top .social-links {
    margin-top: 30px;
}

#footer .footer-top .social-links a {
    font-size: 18px;
    display: inline-block;
    background: #7cc576;
    color: #fff;
    line-height: 1;
    padding: 8px 0;
    margin-right: 4px;
    border-radius: 50%;
    text-align: center;
    width: 36px;
    height: 36px;
    transition: 0.3s;
}

#footer .footer-top .social-links a:hover {
    background: #5ab652;
    color: #fff;
    text-decoration: none;
}

#footer .footer-bottom {
    border-top: 1px solid #222222;
    z-index: 2;
    position: relative;
    padding-top: 40px;
    padding-bottom: 40px;
}

@media only screen and (max-width: 767px) {
    #footer .footer-top {
        position: relative;
        z-index: 2;
        text-align: center;
        padding: 2rem 0 !important;
    }

    #footer .footer-bottom {
        border-top: 1px solid #222222;
        z-index: 2;
        position: relative;
        padding-top: 30px;
        padding-bottom: 16px;
    }
}


#footer .copyright {
    text-align: center;
}

#footer .credits {
    text-align: center;
    font-size: 13px;
    padding-top: 5px;
}


/* ============ Cookie Section ============== */

.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #333;
    padding: 15px;
    text-align: center;
    display: none;
    z-index: 1000;
}
.cookie-consent p{
    color: #fff;
    font-weight: 400;
    font-family: 'Itim', cursive;
}
.cookie-consent button {
    background-color: #4CAF50;
    color: #000;
    font-weight: 700;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
}


@media only screen and (max-width: 767px) {
    .small_p p{
    font-size: 12px !important;
}
}

@media only screen and (max-width: 767px) {
    .wrapper i:first-child {
        top: 13rem !important;
        left: -1rem !important;
        z-index: 1;
    }

    .wrapper i:last-child {
        top: 13rem !important;
        right: -1rem !important;
        z-index: 1;

    }

    .home-text {
        margin-top: 2rem;
    }

    .home-text h1 {
        font-size: 1.7rem;
    }

    .main-text {
        padding: 0 !important;
        margin: 0 !important;
        font-size: 1.8rem !important;
    }

    .contents-left p {
       margin-top: 0.55rem;
        font-size: 0.9rem !important;
    }

    .contents-right p {
        font-size: 0.9rem !important;
    }

    main > section, main > div > section {
        padding-top: 2rem;
        padding-bottom: -5rem;
    }

    .about-us {
        padding: 0 !important;
        margin-top: -2rem !important;
    }


    .center-image {
        margin-top: 0;
        width: 12rem;
    }

    .carousel .card {
        width: 111% ;
    }

    .card h2 {
        font-weight: 500;
        font-size: 1.3rem;
        margin: 30px 0 5px;
    }

    .card {
        text-align: center !important;
    }

    #testimonials {
        padding: 0;
        margin-top: -3rem;
    }
    .testimonials-section {
        padding: 1rem;
    }
    .testimonial-card h3 {
        font-size: 1.1rem;
    }

    .testimonial-card p {
        font-size: 0.9rem;
    }

    .testimonial-text {
        font-size: 0.8rem !important;
    }

    .pricing_p {
        margin-top: 2rem !important;
    }

    .register-heading {
        font-size: 1.3rem !important;
    }

    .register-paragraph {
        font-size: 0.9rem !important;
    }

    .register-button {
        margin-top: 0 !important;
    }

    .faq_section {
        margin-top: 1rem;
    }

    .alter-text {
        font-size: 1.5rem !important;
    }

    .down-text {
        width: 90%;
        margin-top: -2rem;
        font-size: 0.9rem !important;
        text-align: center;
    }

    .down_img {
        margin-top: 1rem;
        width: 10rem;
        height: auto;
    }

    .down-sec {
        padding: 0 !important;
        margin: 0 !important;
    }

    .head {
        font-size: 1.5rem !important;
    }

    .add1 {
        margin-top: 2rem;
    }

    .add1, .add2 {
        margin-bottom: 1rem;
    }

    #contact {
        margin-top: -1rem !important;
    }

    .get_in {
        font-size: 2.5rem !important;
    }

    .touch input, .touch textarea  {
        width: 85vw !important;
        margin-left: -3rem;
    }

    .img-cont, .img-cont img {
        width: 10rem !important;
        height: 10Rem !important;
    }

}