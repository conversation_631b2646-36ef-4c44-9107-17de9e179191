<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Preload the FSEX300 font to make loader more responsive -->
    <link rel="preload" href="  /static/fonts/FSEX300.ttf" as="font" type="font/ttf" crossorigin>

    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Fast, simple inline loader script to show loader immediately when page starts loading
        (function() {
            // Create a fast, simple loader with just LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <style>
                        /* Font is preloaded for faster loading */
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: swap; /* This helps with font loading */
                        }

                        /* Simple loader text with fallback fonts */
                        .loader-text {
                            font-family: 'FSEX300', Consolas, 'Courier New', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN</div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students</title>
     
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">


            <!-- Disable Right click -->

      

    <!-- disable Print Screen for Windows -->

      

    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      

    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Comfortaa', sans-serif;
            background-color: #f5f0f0;
        }

        .card-primary {
            background-color: #1572E8;
            color: white;
            border-radius: 15px;
        }

        .card-info {
            background-color: #48ABF7;
            color: white;
            border-radius: 15px;
        }

        .card-success {
            background-color: #31CE36;
            color: white;
            border-radius: 15px;
        }

        .card-secondary {
            background-color: #6861CE;
            color: white;
            border-radius: 15px;
        }

        .icon-big {
            font-size: 3rem;
        }

        .card-round {
            border-radius: 15px;
        }

        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            background-color: #343a40;
            color: #fff;
            padding-top: 20px;
            transition: all 0.3s;
        }

        .sidebar h4 {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #495057;
        }

        .sidebar a {
            color: #fff;
            padding: 15px 10px;
            padding-left: 25px;
            display: block;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s;
        }

        .sidebar a:hover,
        .sidebar a.active {
            background-color: #495057;
            border-left: 3px solid #007bff;

        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #343a40;
            color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        .dropdown-content a {
            color: white;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

        .show {
            display: block;
        }


        .main-content {
            margin-left: 250px;
            padding: 4px;
            transition: all 0.3s;
        }

        .navbar {
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            background-color: #f0f0f0;
            box-shadow: 0px 4px 6px 1px rgba(150, 142, 150, 1);
        }

        .nav-item a img {
            width: 25px;
            padding-bottom: 4px;
        }

        #dropdown-menu {
            width: 350px;
            padding: 20px;

        }

        hr {
            width: 100%;
            color: black;
            margin-bottom: 0px;
        }

        .viewbtn {
            background-color: #050505;
            width: 90px;
            font-size: 11px;
            color: #fff;
            margin-left: 110px;
            padding-top: 10px;
        }

        .viewbtn:hover {
            background-color: white;
            color: black;
            border: 1px solid black;
        }


        .profile-img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            /* margin-bottom: 15px; */
            border-radius: 50%;
            margin-left: 10px;
        }



        .form-control:focus {
            /* border: none;
            outline: none; */
            box-shadow: none;
        }










        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                display: none;
                /* Adjusted to include display: none */
            }

            .navbar {
                margin-left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .navbar-toggler {
                display: none;
            }



        }

        /* Footer Menu */
        .footer-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            background-color: #f1f8ff;
            padding: 10px 0;
            z-index: 1000;
        }

        .footer-menu a,
        .footer-menu .settings-link {
            color: #000000;
            font-size: 24px;
            text-align: center;
            text-decoration: none;
            position: relative;
        }

        .footer-menu a.active i,
        .footer-menu .settings-link.active i {
            color: #020202;
        }

        .footer-menu .settings-link .submenu {
            display: none;
            position: absolute;
            bottom: 65px;
            left: -250px;
            background-color: #ffffff;
            border: 1px solid #000000;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.226);
            padding: 10px;
            z-index: 1001;
            white-space: nowrap;
        }

        .footer-menu .settings-link a {
            display: block;
            color: #000000;
            padding: 5px 10px;
            text-decoration: none;
            text-align: start;
            margin-top: 10px;
        }

        .footer-menu .settings-link a:hover {
            background-color: #000000;
            color: white;
            border-radius: 3px;
        }

        .footer-menu .settings-link.active {
            display: block;
        }

        .submenu {
            display: none;
            position: absolute;
            background-color: #fff;
            box-shadow: 0 0 10px rgb(192, 221, 253);
            z-index: 1000;
            left: 0;
            margin-top: 5px;
            padding: 10px;
            height: auto;
            width: 310px;
        }

        .sub-submenu {
            display: none;
            position: absolute;
            left: 100%;
            /* Position sub-submenu to the right of submenu */
            top: 0;
            margin-top: -10px;
            /* Adjust top margin as needed */
            padding: 5px 0;
            /* Add padding to sub-submenu */
            background-color: #f9f9f9;
            /* Adjust background color */
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.226);
            /* Optional: Add shadow for better visibility */
        }

        .settings-link:hover .submenu,
        .settings-link:focus .submenu {
            display: block;
        }

        .sub-submenu {
            display: none;
        }

        .submenu-item:hover .sub-submenu,
        .submenu-item:focus .sub-submenu {
            display: block;
        }

        .btn-success {
            background-color: white;
            color: #000000;
        }

        .btn-success:hover {
            background-color: green;
            color: white;
        }

        .btn-danger {
            background-color: white;
            color: rgb(0, 0, 0);
        }

        .btn-danger:hover {
            background-color: rgb(230, 17, 17);
            color: rgb(255, 255, 255);
        }

        .btn-link {
            color: #000000;
            padding-left: 2px;
            margin-bottom: -12px;
        }

        .btn-link:hover {
            color: #000000;
            text-decoration: none;
        }

        .submenu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            /* Example text color */
        }

        .submenu a:hover {
            background-color: #f0f0f0;
            /* Example hover background color */
        }

        .profile-container {
            height: 200px;
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            /* Example background color */
            padding: 20px;
            justify-content: center;
        }

        .profile-photo {
            /* align-items: center; */
            width: 150px;
            height: 150px;
            border-radius: 50%;
            /* Ensures the photo is circular */
            object-fit: cover;
            /* Ensures the photo fills the circle without stretching */
            /* Replace with your actual image URL */
            background-image: url('/static/assets/img/user_149071.png');
            background-position: center;
            background-size: cover;
        }

        .footer {
            /* background-color: #dad9df; */
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin-top: 10px;
            color: #677488;
            opacity: 60%;

        }

        .footer img {
            width: 300px;
            padding-top: 10px;
            opacity: 60%;

        }

        /*
        .footer p {
            font-size: 12px;
            padding-top: 5px;
        } */

        @media (max-width:768px) {
            .footer {
                margin-bottom: 100px;
            }

            .footer img {
                width: 60%;
            }

        }

        @media (max-width: 768px) {

            .table td,
            .table th {
                white-space: nowrap;
            }
        }

        @media (max-width: 576px) {
            .table {
                font-size: 0.8rem;
            }

            .table td,
            .table th {
                padding: 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .table-responsive thead {
                display: none;
            }

            tfoot {
                display: none;
            }

            .table-responsive tr {
                display: block;
                margin-bottom: 10px;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                overflow: hidden;
            }

            .table-responsive td {
                display: flex;
                justify-content: space-between;
                border-top: none;
                padding: 10px;
                background-color: #f8f9fa;
            }

            .table-responsive td:before {
                content: attr(data-label);
                font-weight: bold;
                flex-basis: 45%;
                text-align: left;
                color: #495057;
            }

            .table-responsive td a {
                color: #007bff;
            }
        }

        .table-responsive td,
        .table-responsive th {
            vertical-align: middle;
            text-align: center;
        }



        /* .card-header {
            background-color: #343a40;
            color: #fff;
        } */
        .card-title {
            margin: 0;
        }

        .icon-text-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .icon-text-wrapper i {
            font-size: 24px;
            /* Adjust the icon size as needed */
            margin-bottom: 5px;
            /* Space between icon and text */
        }

        .dashboard-text {
            font-size: 12px;
            /* Match the font size to the icon size */
            line-height: 24px;
            /* Set the line-height to match the icon's height */
        }

        @media only screen and (min-width: 768px) {
            .responsive-section {
                display: none;
            }
        }

        @media only screen and (max-width: 767px) {
            .responsive-section {
                display: block;
            }
        }

        .table-responsive-sm {
            overflow-x: hidden;
        }

        .table td,
        .table th {
            white-space: normal;
            /* Allow text to wrap */
            word-wrap: break-word;
            /* Break long words */
        }

        .table th {
            text-align: left;
            /* Align text to the left */
            vertical-align: top;
            /* Align text to the top */
        }

        .table-responsive-sm table {
            table-layout: fixed;
            /* Ensure the table stays within its container */
        }

        .table-responsive-sm {
            width: 100%;
            margin-bottom: 15px;
            overflow-y: hidden;
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }

        .table-responsive-sm::-webkit-scrollbar {
            display: none;
            /* Hide the scrollbar */
        }

        .card {
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .profile-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .demo-image-container {
            width: 100%;
            height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0;
        }

        .demo-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 64px;
            font-weight: bold;
        }
        @media only screen and (max-width: 767px) {
            .small_p p{
            font-size: 12px !important;

        }

        .shift {
            display: grid;
            grid-template-columns: 100%;
            justify-content: center;
            align-items: center;
        }
        }

    </style>
</head>

<body>

    <!-- Sidebar -->
    {% include "sidebar.html" %}

    <!-- main content -->

    <div class="container">

        <!-- <h3 class="text-center alert alert-success">{{ std.name }}'Student Profile</h3> -->
        <div class="row mt-2">
            <div class="col-md-12">
                <div class="d-flex small_p">
                    <p id="date" class="mr-3"></p>
                    <p id="time" class="mr-3"></p>
                    <!-- <p id="location">Location: {{location}}</p> -->
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-5">
                <div class="row">
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            {% if std.image %}
                                <img src="/media/{{ std.image.url }}" class="img-fluid profile-img" alt="{{ std.name }}'s profile picture" loading="lazy" />
                            {% else %}
                                <div class="demo-image-container">
                                    <div class="demo-image">
                                        {{ std.name|make_list|first|upper }}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Recent Invoices</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered table-hover">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Invoice ID</th>
                                                <th>Issue Date</th>
                                                <th>Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for inv in recent_invoices %}
                                            <tr>
                                                <td data-label="Invoice ID"><a
                                                        href="/students/invoice_student/{{ inv.slug }}">
                                                        {{inv.invoice_id }}</a></td>
                                                <td data-label="Issue Date">{{ inv.issue_date }}</td>
                                                <td data-label="Amount">{{ inv.total_amount}}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-7 d-none d-md-block">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Heading</th>
                                        <th>Data</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Registration Id</td>
                                        <td>{{ std.unique_id }}</td>
                                    </tr>
                                    <tr>
                                        <td>Course Name</td>
                                        <td>{{ std.course }}</td>
                                    </tr>
                                    <tr>
                                        <td>Name</td>
                                        <td>{{ std.name }}</td>
                                    </tr>
                                    <!-- <tr>
                                        <td>Father's Name</td>
                                        <td>{{ std.f_name }}</td>
                                    </tr> -->
                                    <!-- <tr>
                                        <td>Age</td>
                                        <td>{{ std.age }}</td>
                                    </tr> -->
                                    <tr>
                                        <td>Gender</td>
                                        <td>{{ std.gender }}</td>
                                    </tr>
                                    <tr>
                                        <td>Email</td>
                                        <td>{{ std.email }}</td>
                                    </tr>
                                    <tr>
                                        <td>Mobile Number</td>
                                        <td>{{ std.mobile }}</td>
                                    </tr>
                                    <tr>
                                        <td>Address</td>
                                        <td>{{ std.locality }}, {{ std.city }}, {{ std.state }}</td>
                                    </tr>
                                    <tr>
                                        <td>Registration Date</td>
                                        <td>{{ std.registration_date }}</td>
                                    </tr>
                                    <tr>
                                        <td>Registration Fee</td>
                                        <td>{{ std.registration_fee }}</td>
                                    </tr>
                                    <tr>
                                        <td>Shift</td>
                                        <td>
                                            {% for shift in invoice.shift.all %}
                                            {{ shift.name }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Time Slot</td>
                                        <td>
                                            {% for shift in invoice.shift.all %}
                                            {{ shift.time_range }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Seats Number</td>
                                        <td>
                                            {% for seat in booking_seats %}
                                            {{ seat.seat.seat_number }} - {{seat.seat.shift}}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Months</td>
                                        <td>
                                            {% for month in invoice.months.all %}
                                            {{ month.name }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Payment Date</td>
                                        <td>{{ invoice.issue_date }}</td>
                                    </tr>
                                    <tr>
                                        <td>Due Date</td>
                                        <td>{{ invoice.due_date }}</td>
                                    </tr>
                                    <tr>
                                        <td>Mode of Payment</td>
                                        <td>{{ invoice.mode_pay }}</td>
                                    </tr>
                                    <tr>
                                        <td>Status</td>
                                        <td>{{ invoice.is_active }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <a href="/students/update/{{ std.slug }}" class="btn btn-primary">Update</a>
                            {% if fee_status.is_paid == False %}
                            <a href="/students/create_registration/{{ std.slug }}"
                                class="btn btn-danger mx-auto">Registration Fee</a>
                            {% endif %}
                            <a href="/students/create_invoice/{{ std.slug }}" class="btn btn-success">Pay</a>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-12 responsive-section">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover table-responsive-sm">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Registration Id</th>
                                        <th>Course Name</th>
                                        <th>Name</th>
                                        <th>Father's Name</th>
                                        <th>Age</th>
                                        <th>Gender</th>
                                        <th>Email</th>
                                        <th>Mobile Number</th>
                                        <th>Address</th>
                                        <th>Registration Date</th>
                                        <th>Registration Fee</th>
                                        <th>Shift</th>
                                        <th>Time Slot</th>
                                        <th>Months</th>
                                        <th>Payment Date</th>
                                        <th>Due Date</th>
                                        <th>Mode of Payment</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td data-label="Registration Id">{{ std.unique_id }}</td>
                                        <td data-label="Course Name">{{ std.course }}</td>
                                        <td data-label="Name">{{ std.name }}</td>
                                        <td data-label="Father's Name">{{ std.f_name }}</td>
                                        <td data-label="Age">{{ std.age }}</td>
                                        <td data-label="Gender">{{ std.gender }}</td>
                                        <td data-label="Email">{{ std.email }}</td>
                                        <td data-label="Mobile Number">{{ std.mobile }}</td>
                                        <td data-label="Address">{{ std.locality }}, {{ std.city }}, {{ std.state }}
                                        </td>
                                        <td data-label="Registration Date">{{ std.registration_date }}</td>
                                        <td data-label="Registration Fee">{{ std.registration_fee }}</td>
                                        <td data-label="Shift">
                                            {% for shift in invoice.shift.all %}
                                            {{ shift.name }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td data-label="Time Slot">
                                            {% for shift in invoice.shift.all %}
                                            {{ shift.time_range }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td data-label="Seats Number">
                                            {% for seat in booking_seats %}
                                            {{ seat.seat.seat_number }} - {{seat.seat.shift}}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td data-label="Months">
                                            {% for month in invoice.months.all %}
                                            {{ month.name }}{% if not forloop.last %} | {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td data-label="Payment Date">{{ invoice.issue_date }}</td>
                                        <td data-label="Due Date">{{ invoice.due_date }}</td>
                                        <td data-label="Mode of Payment">{{ invoice.mode_pay }}</td>
                                        <td data-label="Status">{{ invoice.is_active }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <a href="/students/update/{{ std.slug }}" class="btn btn-primary">Update</a>
                            {% if fee_status.is_paid == False %}
                            <a href="/students/create_registration/{{ std.slug }}"
                                class="btn btn-danger mx-auto">Registration Fee</a>
                            {% endif %}
                            <a href="/students/create_invoice/{{ std.slug }}" class="btn btn-success">Pay</a>
                        </div>
                    </div>
                </div>
            </div>




        </div>
    </div>
    <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">        <!-- <p>Developed with passion by Librainian</p> -->
    </div>

    </div>



    <!-- JavaScript dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        document.getElementById("userDropdown").addEventListener("click", function () {
            document.getElementById("userDropdownContent").classList.toggle("show");
        });

        // Close the dropdown if the user clicks outside of it
        window.addEventListener("click", function (event) {
            if (!event.target.matches("#userDropdown")) {
                var dropdowns = document.getElementsByClassName("dropdown-content");
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains("show")) {
                        openDropdown.classList.remove("show");
                    }
                }
            }
        });

    </script>
    <script>
        function updateDateTime() {
            const now = new Date();
            const date = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
            const time = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

            document.getElementById('date').textContent = 'Date: ' + date;
            document.getElementById('time').textContent = 'Time: ' + time;
        }

        // Update the date and time on page load
        updateDateTime();

        // Update the date and time every minute
        setInterval(updateDateTime, 60000);
    </script>
    <script>
         
    </script>
    <script>
        // Get the menu icon and submenu elements
        const menuIcon = document.getElementById('menu-icon');
        const submenu = document.getElementById('submenu');

        // Add click event listener to the menu icon
        menuIcon.addEventListener('click', function () {
            submenu.classList.toggle('show');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var menuIcon = document.getElementById('menu-icon');
            var submenu = document.getElementById('submenu');

            // Toggle submenu visibility on menu icon click
            menuIcon.addEventListener('click', function () {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var footerSearch = document.getElementById('footer-search');
            var exampleModal = new bootstrap.Modal(document.getElementById('exampleModal'));

            footerSearch.addEventListener('click', function () {
                exampleModal.show();
            });
        });
    </script>
    <!-- <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css"> -->

    <!-- Global Loader Script -->
     
</body>

</html>