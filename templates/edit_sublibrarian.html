{% include "baseTemplate.html" %}

<!-- Sidebar -->
{% include "sidebar.html" %}

<div class="main-content">
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-xl-8 col-lg-10 col-md-11 col-sm-12">
                <!-- Page Title -->
                <div class="d-flex justify-content-center mb-4">
                    <div class="alert alert-success text-center" role="alert" style="width: 100%; max-width: 30rem;">
                        DASHBOARD / EDIT SUBLIBRARIAN
                    </div>
                </div>

                <!-- Card Container -->
                <div class="card shadow border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-user-edit me-2"></i>
                            <span>Edit Sublibrarian Details</span>
                        </h4>
                    </div>
                    <div class="card-body p-3 p-md-4">
                        <form method="POST">
                            {% csrf_token %}

                            <div class="row g-3">
                                <!-- Personal Information Section -->
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <label for="first_name" class="form-label">First Name:</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" id="first_name" name="first_name" class="form-control" value="{{ sub.user.first_name }}" required>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <label for="last_name" class="form-label">Last Name:</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" id="last_name" name="last_name" class="form-control" value="{{ sub.user.last_name }}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <label for="email" class="form-label">Email Address:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" id="email" name="email" class="form-control" value="{{ sub.user.email }}" required>
                                </div>
                            </div>

                            <div class="mt-3">
                                <label for="phone" class="form-label">Phone Number:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input type="text" id="phone" name="phone" class="form-control" value="{{ sub.sublibrarian_phone_num }}" required>
                                </div>
                            </div>

                            <div class="mt-3">
                                <label for="address" class="form-label">Address:</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <input type="text" id="address" name="address" class="form-control" value="{{ sub.sublibrarian_address }}" required>
                                </div>
                            </div>

                            <!-- Buttons for mobile and desktop -->
                            <div class="mt-4">
                                <!-- Mobile view: stacked buttons (full width) -->
                                <div class="d-grid gap-2 d-md-none">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Save Changes
                                    </button>
                                    <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Cancel
                                    </a>
                                </div>

                                <!-- Desktop view: side by side buttons -->
                                <div class="d-none d-md-flex justify-content-end gap-2">
                                    <a href="javascript:history.back()" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Save Changes
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Extra spacing for mobile to account for footer menu -->
                <div class="d-block d-md-none" style="height: 80px;"></div>
            </div>
        </div>
    </div>
</div>
