<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/css/upgrade_plan.css">
    <title>Upgrade Your Subscription Plan</title>
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">


</head>
<body>

    <div class="container" >
        <div class="header">
            <h2>Upgrade Your Subscription Plan</h2>
            <p>Review your current plan and explore premium options to enhance your experience.</p>
        </div>
        <div class="plan-details">
            <h5>Current Plan Details</h5>
            <ul class="list-group">
                <li class="list-group-item"><i class="fas fa-box"></i> Plan Name: {{membership.plan.name}}</li>
                <li class="list-group-item"><i class="fas fa-tag"></i> Price: {{membership.plan.price}}</li>
                <li class="list-group-item"><i class="fas fa-calendar-alt"></i> Start Date: {{membership.start_date}}</li>
                <li class="list-group-item"><i class="fas fa-calendar-check"></i> Expiry Date: {{membership.expiry_date}}</li>
                <li class="list-group-item"><i class="fas fa-list"></i> Features: {{membership.plan.description_line_01}}, {{membership.plan.description_line_02}}, {{membership.plan.description_line_03}} </li>
            </ul>
        </div>
        <div class="d-flex justify-content-between flex-wrap">
            <a href="/membership/plans/" class="btn btn-primary btn-custom mb-2">Upgrade Plan</a>
            <a href="/librarian/dashboard/" class="btn btn-outline-secondary btn-custom mb-2">Return to Dashboard</a>
        </div>
    </div>

    <div class="footer">
        <p>&copy; 2025 Librarian. All Rights Reserved. | <a href="/terms">Terms of Service</a> | <a href="/privacy">Privacy Policy</a></p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
