<!DOCTYPE html>
<html lang="en">


<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     
    <title>{% if blog %}Edit Blog{% else %}Create Blog{% endif %}</title>
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="https://i.postimg.cc/ZY3b6dw9/librainian-logo-black-transparent-med.png" rel="icon">




    <style>
        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: #f5f0f0;
            font-family: 'Comfortaa', sans-serif;
        }

        .card {
            max-width: 23rem;
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .card-text {
            color: #6c757d;
            font-size: 1rem;
        }

        .btn-group .btn {
            padding: 6px 12px;
            font-size: 0.875rem;
        }

        .copy-status {
            display: inline;
            color: green;
            font-size: 0.875rem;
        }
    </style>
</head>

<body>

<!-- Sidebar -->
{% include "sidebar.html" %}

<!-- Dashboard Content -->
<div class="container mt-4">
    <h3 class="text-center mb-2">Blog List</h3>

    <!-- Messages Section -->
    {% if messages %}
    <div id="messageContainer">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="d-flex flex-wrap justify-content-between mb-3">
        <a href="/blogs/create-blog/" class="btn btn-primary"><i class="fas fa-plus"></i> Create New Blog</a>
        <div class="d-flex justify-content-center mx-1 my-1">
            <select id="entriesPerPage" class="form-select me-2">
                <option value="6">6</option>
                <option value="12">12</option>
                <option value="24">24</option>
                <option value="48">48</option>
            </select>
            <input class="mx-1 px-2" type="text" class="form-control" id="searchInput" placeholder="Search Blogs...">
        </div>
    </div>

    <div class="d-flex flex-wrap" id="blogList">
        {% for blog in blogs %}
        <div>
            <div class="card m-2">
                <div class="card-body">
                    <h5 class="card-title">{{ blog.title }}</h5>
                    <p class="card-text">{{ blog.description|truncatewords:15 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="/blogs/p/{{ blog.slug }}/" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="#" class="btn btn-outline-secondary btn-sm" onclick="copyLink('www.librainian.com/blogs/p/{{ blog.slug }}/', this); return false;">
                                <i class="fas fa-link"></i>
                                <span class="copy-status" style="display: none;">Copied!</span>
                            </a>
                            <a href="/blogs/update/{{ blog.slug }}/" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <button class="btn btn-danger btn-sm" onclick="confirmDelete('{{ blog.slug }}')">
                                <i class="fas fa-trash-alt"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <nav aria-label="Blog pagination">
        <ul id="pagination" class="pagination justify-content-center mt-3"></ul>
    </nav>
</div>
</div>


<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this blog?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a id="confirmDeleteButton" href="#" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript dependencies -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
    // Existing JavaScript for pagination, search, etc.
    const blogList = document.getElementById('blogList');
    const searchInput = document.getElementById('searchInput');
    const entriesPerPage = document.getElementById('entriesPerPage');
    const paginationContainer = document.getElementById('pagination');

    let currentPage = 1;
    let allBlogs = [...blogList.children];
    let filteredBlogs = allBlogs;

    function updatePagination() {
        const pageCount = Math.ceil(filteredBlogs.length / parseInt(entriesPerPage.value));
        paginationContainer.innerHTML = '';

        for (let i = 1; i <= pageCount; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            paginationContainer.appendChild(li);
        }
    }

    function changePage(page) {
        currentPage = page;
        displayBlogs();
        updatePagination();
    }

    function displayBlogs() {
    blogList.innerHTML = ''; // Clear current list
    const startIndex = (currentPage - 1) * parseInt(entriesPerPage.value);
    const endIndex = startIndex + parseInt(entriesPerPage.value);

    // Append the filtered blogs into the container
    filteredBlogs.slice(startIndex, endIndex).forEach(blog => {
        blogList.appendChild(blog);
    });

    // Force a reflow to ensure layout recalculates properly
    blogList.offsetHeight; // This line forces a reflow
}


    function filterBlogs() {
        const searchTerm = searchInput.value.toLowerCase();
        filteredBlogs = allBlogs.filter(blog => blog.querySelector('.card-title').textContent.toLowerCase().includes(searchTerm));
        currentPage = 1;
        displayBlogs();
        updatePagination();
    }

    searchInput.addEventListener('input', filterBlogs);
    entriesPerPage.addEventListener('change', () => {
        currentPage = 1;
        displayBlogs();
        updatePagination();
    });

    document.addEventListener('DOMContentLoaded', () => {
        displayBlogs();
        updatePagination();
    });

    function copyLink(url, element) {
        navigator.clipboard.writeText(url).then(() => {
            const status = element.querySelector('.copy-status');
            status.style.display = 'inline';
            setTimeout(() => status.style.display = 'none', 2000);
        });
    }

    function confirmDelete(slug) {
        const deleteButton = document.getElementById('confirmDeleteButton');
        deleteButton.href = `/blogs/delete/${slug}/`;
        $('#deleteModal').modal('show');
    }
</script>

<script>
    // Register the service worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then(function (registration) {
                console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(function (err) {
                console.log('Service Worker registration failed:', err);
            });
    }
</script>
<script>
    window.addEventListener("load", function () {
        const path = window.location.pathname; // Get current page path
        let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

        // Increment count for the current path
        pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

        // Store updated data back to localStorage
        localStorage.setItem("page_data", JSON.stringify(pageData));
      });

      // Function to send page view data
      function sendPageData() {
        const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

        if (Object.keys(pageData).length > 0) {
          fetch(location.origin + "/librarian/track-page-view/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": "{{ csrf_token }}",
            },
            body: JSON.stringify(pageData),
          })

            .then(() => {
              localStorage.removeItem("page_data");
            })
            .catch((error) => console.error("Error sending page data:", error));
            localStorage.removeItem("page_data");
        } else {

          console.log("No page data to send");
        }
      }

      // Send data every 10 seconds
      setInterval(sendPageData, 10000);
</script>
</body>
</html>
