<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ action }} Advertisement</title>


    <!-- Disable Right click -->

      
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
    

    <style>
        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
        }
    </style>

   
</head>
<body>
    <div class="container mt-5">
        <h1>{{ action }} Advertisement</h1>
        <form method="POST" enctype="multipart/form-data">
            {% csrf_token %}
            {% if messages %}
                <div id="messages">
                    {% for message in messages %}
                        <div class="alert alert-success" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            <div class="form-group">
                <label for="subject">Subject</label>
                <input type="text" name="subject" id="subject" class="form-control" value="{{ ad.subject|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="amount">Amount</label>
                <input type="number" step="0.01" name="amount" id="amount" class="form-control" value="{{ ad.amount|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="start_date">Start Date</label>
                <input type="date" name="start_date" id="start_date" class="form-control" value="{{ ad.start_date|date:'Y-m-d'|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="end_date">End Date</label>
                <input type="date" name="end_date" id="end_date" class="form-control" value="{{ ad.end_date|date:'Y-m-d'|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="max_views">Max Views</label>
                <input type="number" name="max_views" id="max_views" class="form-control" value="{{ ad.max_views|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="priority">Priority</label>
                <input type="number" name="priority" id="priority" class="form-control" value="{{ ad.priority|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="age_group">Age Group</label>
                <input type="text" name="age_group" id="age_group" class="form-control" value="{{ ad.age_group|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="gender">Gender</label>
                <input type="text" name="gender" id="gender" class="form-control" value="{{ ad.gender|default_if_none:'' }}">
            </div>
            <div class="form-group">
                <label for="image_vertical">Vertical Image</label>
                <input type="file" name="image_vertical" id="image_vertical" class="form-control">
            </div>
            <div class="form-group">
                <label for="image_horizontal">Horizontal Image</label>
                <input type="file" name="image_horizontal" id="image_horizontal" class="form-control">
            </div>
            <button type="submit" class="btn btn-success">{{ action }} Advertisement</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
